// Copyright (C) 2018  Intel Corporation. All rights reserved.
// Your use of Intel Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Intel Program License 
// Subscription Agreement, the Intel Quartus Prime License Agreement,
// the Intel FPGA IP License Agreement, or other applicable license
// agreement, including, without limitation, that your use is for
// the sole purpose of programming logic devices manufactured by
// Intel and sold by Intel or its authorized distributors.  Please
// refer to the applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8L Package TQFP144
// 

//
// This file contains Fast Corner delays for the design using part EP4CE6E22C8L,
// with speed grade M, core voltage 1.0VmV, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (VHDL) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "LED")
  (DATE "07/31/2025 13:23:38")
  (VENDOR "Altera")
  (PROGRAM "Quartus Prime")
  (VERSION "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[0\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (866:866:866) (943:943:943))
        (IOPATH i o (2024:2024:2024) (2041:2041:2041))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[1\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (790:790:790) (869:869:869))
        (IOPATH i o (1959:1959:1959) (1976:1976:1976))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[2\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (901:901:901) (979:979:979))
        (IOPATH i o (2012:2012:2012) (2041:2041:2041))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[3\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (807:807:807) (877:877:877))
        (IOPATH i o (2012:2012:2012) (2041:2041:2041))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\toggle_pin\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (977:977:977) (1084:1084:1084))
        (IOPATH i o (2024:2024:2024) (2041:2041:2041))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE \\clk\~input\\)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (466:466:466) (733:733:733))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE \\clk\~inputclkctrl\\)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (136:136:136) (113:113:113))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (222:222:222) (292:292:292))
        (PORT datab (210:210:210) (282:282:282))
        (PORT datad (187:187:187) (242:242:242))
        (IOPATH dataa combout (213:213:213) (221:221:221))
        (IOPATH datab combout (232:232:232) (231:231:231))
        (IOPATH datac combout (275:275:275) (276:276:276))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (260:260:260) (256:256:256))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (129:129:129) (151:151:151))
        (PORT datad (453:453:453) (497:497:497))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (197:197:197) (265:265:265))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (197:197:197) (265:265:265))
        (IOPATH dataa combout (254:254:254) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (225:225:225) (246:246:246))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (200:200:200) (267:267:267))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (146:146:146) (179:179:179))
        (PORT datad (452:452:452) (497:497:497))
        (IOPATH datab combout (232:232:232) (231:231:231))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (197:197:197) (265:265:265))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (197:197:197) (265:265:265))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (200:200:200) (267:267:267))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (148:148:148) (181:181:181))
        (PORT datad (453:453:453) (498:498:498))
        (IOPATH datab combout (232:232:232) (231:231:231))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (200:200:200) (266:266:266))
        (IOPATH datab combout (225:225:225) (246:246:246))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (131:131:131) (154:154:154))
        (PORT datad (453:453:453) (497:497:497))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1038:1038:1038) (1047:1047:1047))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (285:285:285) (358:358:358))
        (IOPATH dataa combout (254:254:254) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (211:211:211) (252:252:252))
        (PORT datac (224:224:224) (265:265:265))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (175:175:175) (172:172:172))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1295:1295:1295) (1310:1310:1310))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (277:277:277) (350:350:350))
        (IOPATH datab combout (225:225:225) (246:246:246))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (184:184:184) (215:215:215))
        (PORT datad (213:213:213) (242:242:242))
        (IOPATH datac combout (175:175:175) (174:174:174))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1295:1295:1295) (1310:1310:1310))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1036:1036:1036) (1046:1046:1046))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (288:288:288) (368:368:368))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (186:186:186) (217:217:217))
        (PORT datad (212:212:212) (242:242:242))
        (IOPATH datac combout (175:175:175) (174:174:174))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1295:1295:1295) (1310:1310:1310))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1036:1036:1036) (1046:1046:1046))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (282:282:282) (356:356:356))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (209:209:209) (250:250:250))
        (PORT datac (213:213:213) (248:248:248))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (175:175:175) (172:172:172))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1295:1295:1295) (1310:1310:1310))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (288:288:288) (368:368:368))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (213:213:213) (254:254:254))
        (PORT datac (222:222:222) (263:263:263))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (175:175:175) (172:172:172))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1295:1295:1295) (1310:1310:1310))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (201:201:201) (269:269:269))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (130:130:130) (151:151:151))
        (PORT datad (250:250:250) (277:277:277))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1304:1304:1304) (1335:1335:1335))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (295:295:295) (378:378:378))
        (PORT datab (198:198:198) (265:265:265))
        (PORT datac (276:276:276) (346:346:346))
        (PORT datad (177:177:177) (229:229:229))
        (IOPATH dataa combout (213:213:213) (225:225:225))
        (IOPATH datab combout (217:217:217) (231:231:231))
        (IOPATH datac combout (175:175:175) (174:174:174))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (198:198:198) (265:265:265))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (271:271:271) (316:316:316))
        (PORT datad (127:127:127) (143:143:143))
        (IOPATH dataa combout (213:213:213) (221:221:221))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1304:1304:1304) (1335:1335:1335))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (288:288:288) (364:364:364))
        (IOPATH datab combout (225:225:225) (246:246:246))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (190:190:190) (222:222:222))
        (PORT datad (218:218:218) (250:250:250))
        (IOPATH datac combout (175:175:175) (174:174:174))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1295:1295:1295) (1310:1310:1310))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (196:196:196) (263:263:263))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1036:1036:1036) (1046:1046:1046))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (199:199:199) (267:267:267))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (128:128:128) (149:149:149))
        (PORT datad (249:249:249) (277:277:277))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1304:1304:1304) (1335:1335:1335))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (178:178:178) (230:230:230))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1304:1304:1304) (1335:1335:1335))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (285:285:285) (362:362:362))
        (PORT datab (196:196:196) (263:263:263))
        (PORT datac (388:388:388) (457:457:457))
        (PORT datad (263:263:263) (325:325:325))
        (IOPATH dataa combout (213:213:213) (221:221:221))
        (IOPATH datab combout (227:227:227) (222:222:222))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (289:289:289) (366:366:366))
        (PORT datab (281:281:281) (358:358:358))
        (PORT datac (265:265:265) (330:330:330))
        (PORT datad (271:271:271) (337:337:337))
        (IOPATH dataa combout (232:232:232) (225:225:225))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (176:176:176) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (200:200:200) (267:267:267))
        (PORT datab (196:196:196) (263:263:263))
        (PORT datac (269:269:269) (330:330:330))
        (PORT datad (275:275:275) (341:341:341))
        (IOPATH dataa combout (232:232:232) (225:225:225))
        (IOPATH datab combout (232:232:232) (231:231:231))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (298:298:298) (381:381:381))
        (PORT datab (294:294:294) (375:375:375))
        (PORT datac (273:273:273) (343:343:343))
        (PORT datad (401:401:401) (472:472:472))
        (IOPATH dataa combout (213:213:213) (221:221:221))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (176:176:176) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (288:288:288) (366:366:366))
        (PORT datab (293:293:293) (377:377:377))
        (PORT datac (276:276:276) (349:349:349))
        (PORT datad (398:398:398) (468:468:468))
        (IOPATH dataa combout (232:232:232) (225:225:225))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (241:241:241) (287:287:287))
        (PORT datab (147:147:147) (179:179:179))
        (PORT datac (403:403:403) (438:438:438))
        (PORT datad (408:408:408) (451:451:451))
        (IOPATH dataa combout (214:214:214) (225:225:225))
        (IOPATH datab combout (218:218:218) (231:231:231))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (150:150:150) (183:183:183))
        (PORT datab (294:294:294) (378:378:378))
        (PORT datac (131:131:131) (154:154:154))
        (PORT datad (126:126:126) (143:143:143))
        (IOPATH dataa combout (225:225:225) (221:221:221))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[1\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1343:1343:1343))
        (PORT d (59:59:59) (74:74:74))
        (PORT ena (652:652:652) (679:679:679))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
      (HOLD ena (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (217:217:217) (288:288:288))
        (PORT datab (214:214:214) (288:288:288))
        (PORT datad (194:194:194) (251:251:251))
        (IOPATH dataa combout (213:213:213) (221:221:221))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (275:275:275) (276:276:276))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[2\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1343:1343:1343))
        (PORT d (59:59:59) (74:74:74))
        (PORT ena (652:652:652) (679:679:679))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
      (HOLD ena (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (292:292:292) (367:367:367))
        (PORT datab (215:215:215) (288:288:288))
        (PORT datad (194:194:194) (252:252:252))
        (IOPATH dataa combout (232:232:232) (225:225:225))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (275:275:275) (276:276:276))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[3\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1343:1343:1343))
        (PORT d (59:59:59) (74:74:74))
        (PORT ena (652:652:652) (679:679:679))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
      (HOLD ena (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (222:222:222) (292:292:292))
        (PORT datab (209:209:209) (282:282:282))
        (PORT datad (187:187:187) (242:242:242))
        (IOPATH dataa combout (213:213:213) (221:221:221))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (275:275:275) (276:276:276))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[0\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1343:1343:1343))
        (PORT d (59:59:59) (74:74:74))
        (PORT ena (652:652:652) (679:679:679))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
      (HOLD ena (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (186:186:186) (257:257:257))
        (IOPATH datab combout (260:260:260) (256:256:256))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (129:129:129) (151:151:151))
        (PORT datad (364:364:364) (396:396:396))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (197:197:197) (265:265:265))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (189:189:189) (259:259:259))
        (IOPATH dataa combout (254:254:254) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (225:225:225) (246:246:246))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (225:225:225) (246:246:246))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (198:198:198) (265:265:265))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (147:147:147) (179:179:179))
        (PORT datad (364:364:364) (395:395:395))
        (IOPATH datab combout (232:232:232) (231:231:231))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (197:197:197) (265:265:265))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (197:197:197) (265:265:265))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (195:195:195) (262:262:262))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (202:202:202) (269:269:269))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (131:131:131) (154:154:154))
        (PORT datad (363:363:363) (395:395:395))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1054:1054:1054))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (280:280:280) (352:352:352))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (221:221:221) (266:266:266))
        (PORT datac (226:226:226) (269:269:269))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (175:175:175) (172:172:172))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1347:1347:1347))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (276:276:276) (349:349:349))
        (IOPATH datab combout (225:225:225) (246:246:246))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (190:190:190) (222:222:222))
        (PORT datad (212:212:212) (242:242:242))
        (IOPATH datac combout (175:175:175) (174:174:174))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1043:1043:1043) (1055:1055:1055))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (280:280:280) (355:355:355))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (196:196:196) (230:230:230))
        (PORT datad (222:222:222) (256:256:256))
        (IOPATH datac combout (175:175:175) (174:174:174))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1347:1347:1347))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (198:198:198) (265:265:265))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1053:1053:1053))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (201:201:201) (269:269:269))
        (IOPATH dataa combout (254:254:254) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (130:130:130) (152:152:152))
        (PORT datad (252:252:252) (278:278:278))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1312:1312:1312) (1342:1342:1342))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (198:198:198) (265:265:265))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1044:1044:1044) (1053:1053:1053))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (199:199:199) (265:265:265))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (274:274:274) (317:317:317))
        (PORT datad (127:127:127) (144:144:144))
        (IOPATH dataa combout (213:213:213) (221:221:221))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1312:1312:1312) (1342:1342:1342))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (284:284:284) (357:357:357))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (218:218:218) (260:260:260))
        (PORT datac (219:219:219) (259:259:259))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (175:175:175) (172:172:172))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1347:1347:1347))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (455:455:455) (533:533:533))
        (PORT datab (285:285:285) (366:366:366))
        (PORT datac (266:266:266) (331:331:331))
        (PORT datad (179:179:179) (230:230:230))
        (IOPATH dataa combout (213:213:213) (225:225:225))
        (IOPATH datab combout (217:217:217) (231:231:231))
        (IOPATH datac combout (175:175:175) (174:174:174))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (280:280:280) (354:354:354))
        (IOPATH datab combout (260:260:260) (249:249:249))
        (IOPATH datab cout (294:294:294) (236:236:236))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (190:190:190) (222:222:222))
        (PORT datad (210:210:210) (237:237:237))
        (IOPATH datac combout (175:175:175) (174:174:174))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1347:1347:1347))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (200:200:200) (268:268:268))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (274:274:274) (317:317:317))
        (PORT datad (126:126:126) (143:143:143))
        (IOPATH dataa combout (213:213:213) (221:221:221))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1312:1312:1312) (1342:1342:1342))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (290:290:290) (371:371:371))
        (IOPATH dataa combout (254:254:254) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (214:214:214) (257:257:257))
        (PORT datac (212:212:212) (246:246:246))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (175:175:175) (172:172:172))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1347:1347:1347))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (198:198:198) (266:266:266))
        (IOPATH dataa combout (224:224:224) (244:244:244))
        (IOPATH dataa cout (290:290:290) (234:234:234))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
        (IOPATH cin cout (46:46:46) (46:46:46))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1312:1312:1312) (1342:1342:1342))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (268:268:268) (330:330:330))
        (IOPATH datad combout (95:95:95) (89:89:89))
        (IOPATH cin combout (275:275:275) (299:299:299))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (197:197:197) (231:231:231))
        (PORT datad (217:217:217) (249:249:249))
        (IOPATH datac combout (175:175:175) (174:174:174))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1347:1347:1347))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (201:201:201) (269:269:269))
        (PORT datab (292:292:292) (372:372:372))
        (PORT datac (274:274:274) (346:346:346))
        (PORT datad (181:181:181) (232:232:232))
        (IOPATH dataa combout (225:225:225) (221:221:221))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (289:289:289) (366:366:366))
        (PORT datab (290:290:290) (371:371:371))
        (PORT datac (425:425:425) (493:493:493))
        (PORT datad (262:262:262) (324:324:324))
        (IOPATH dataa combout (232:232:232) (225:225:225))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (176:176:176) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (200:200:200) (268:268:268))
        (PORT datab (199:199:199) (265:265:265))
        (PORT datac (179:179:179) (236:236:236))
        (PORT datad (267:267:267) (328:328:328))
        (IOPATH dataa combout (232:232:232) (225:225:225))
        (IOPATH datab combout (232:232:232) (231:231:231))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (297:297:297) (381:381:381))
        (PORT datab (291:291:291) (371:371:371))
        (PORT datac (433:433:433) (499:499:499))
        (PORT datad (262:262:262) (322:322:322))
        (IOPATH dataa combout (232:232:232) (225:225:225))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (176:176:176) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (191:191:191) (261:261:261))
        (PORT datab (185:185:185) (255:255:255))
        (PORT datac (267:267:267) (328:328:328))
        (PORT datad (264:264:264) (319:319:319))
        (IOPATH dataa combout (213:213:213) (221:221:221))
        (IOPATH datab combout (217:217:217) (222:222:222))
        (IOPATH datac combout (176:176:176) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (246:246:246) (293:293:293))
        (PORT datab (145:145:145) (177:177:177))
        (PORT datac (406:406:406) (442:442:442))
        (PORT datad (574:574:574) (619:619:619))
        (IOPATH dataa combout (214:214:214) (225:225:225))
        (IOPATH datab combout (218:218:218) (231:231:231))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (146:146:146) (180:180:180))
        (PORT datab (200:200:200) (266:266:266))
        (PORT datac (131:131:131) (153:153:153))
        (PORT datad (127:127:127) (144:144:144))
        (IOPATH dataa combout (214:214:214) (225:225:225))
        (IOPATH datab combout (218:218:218) (231:231:231))
        (IOPATH datac combout (175:175:175) (172:172:172))
        (IOPATH datad combout (95:95:95) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_pin\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (220:220:220) (265:265:265))
        (IOPATH datab combout (263:263:263) (288:288:288))
        (IOPATH datac combout (275:275:275) (276:276:276))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_pin\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1311:1311:1311) (1347:1347:1347))
        (PORT d (59:59:59) (74:74:74))
        (IOPATH (posedge clk) q (164:164:164) (164:164:164))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (127:127:127))
    )
  )
)
