// Copyright (C) 2018  Intel Corporation. All rights reserved.
// Your use of Intel Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Intel Program License 
// Subscription Agreement, the Intel Quartus Prime License Agreement,
// the Intel FPGA IP License Agreement, or other applicable license
// agreement, including, without limitation, that your use is for
// the sole purpose of programming logic devices manufactured by
// Intel and sold by Intel or its authorized distributors.  Please
// refer to the applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8L Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8L,
// with speed grade 8L, core voltage 1.0VmV, and temperature 85 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (VHDL) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "LED")
  (DATE "07/31/2025 13:23:38")
  (VENDOR "Altera")
  (PROGRAM "Quartus Prime")
  (VERSION "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[0\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1561:1561:1561) (1554:1554:1554))
        (IOPATH i o (3329:3329:3329) (3302:3302:3302))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[1\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1423:1423:1423) (1431:1431:1431))
        (IOPATH i o (3218:3218:3218) (3208:3208:3208))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[2\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1616:1616:1616) (1617:1617:1617))
        (IOPATH i o (3313:3313:3313) (3290:3290:3290))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[3\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1446:1446:1446) (1448:1448:1448))
        (IOPATH i o (3313:3313:3313) (3290:3290:3290))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\toggle_pin\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1746:1746:1746) (1788:1788:1788))
        (IOPATH i o (3329:3329:3329) (3302:3302:3302))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE \\clk\~input\\)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (825:825:825) (838:838:838))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE \\clk\~inputclkctrl\\)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (220:220:220) (206:206:206))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (393:393:393) (524:524:524))
        (PORT datab (381:381:381) (508:508:508))
        (PORT datad (340:340:340) (438:438:438))
        (IOPATH dataa combout (478:478:478) (509:509:509))
        (IOPATH datab combout (462:462:462) (531:531:531))
        (IOPATH datac combout (515:515:515) (545:545:545))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (354:354:354) (468:468:468))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (234:234:234) (265:265:265))
        (PORT datad (831:831:831) (842:842:842))
        (IOPATH datac combout (337:337:337) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (358:358:358) (475:475:475))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (358:358:358) (475:475:475))
        (IOPATH dataa combout (471:471:471) (532:532:532))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (354:354:354) (468:468:468))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (354:354:354) (468:468:468))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (361:361:361) (477:477:477))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (265:265:265) (312:312:312))
        (PORT datad (830:830:830) (841:841:841))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (354:354:354) (468:468:468))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (359:359:359) (475:475:475))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (354:354:354) (468:468:468))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (358:358:358) (475:475:475))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (358:358:358) (473:473:473))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (267:267:267) (318:318:318))
        (PORT datad (831:831:831) (842:842:842))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (358:358:358) (473:473:473))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (237:237:237) (273:273:273))
        (PORT datad (831:831:831) (842:842:842))
        (IOPATH datac combout (337:337:337) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1753:1753:1753) (1774:1774:1774))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (527:527:527) (627:627:627))
        (IOPATH dataa combout (471:471:471) (532:532:532))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (370:370:370) (437:437:437))
        (PORT datac (416:416:416) (459:459:459))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datac combout (337:337:337) (353:353:353))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2220:2220:2220) (2222:2222:2222))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (512:512:512) (613:613:613))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (328:328:328) (382:382:382))
        (PORT datad (395:395:395) (422:422:422))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2220:2220:2220) (2222:2222:2222))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (354:354:354) (468:468:468))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1751:1751:1751) (1772:1772:1772))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (539:539:539) (647:647:647))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (330:330:330) (384:384:384))
        (PORT datad (394:394:394) (420:420:420))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2220:2220:2220) (2222:2222:2222))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (354:354:354) (469:469:469))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1751:1751:1751) (1772:1772:1772))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (521:521:521) (625:625:625))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (369:369:369) (435:435:435))
        (PORT datac (393:393:393) (428:428:428))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datac combout (337:337:337) (353:353:353))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2220:2220:2220) (2222:2222:2222))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (539:539:539) (645:645:645))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (372:372:372) (439:439:439))
        (PORT datac (412:412:412) (455:455:455))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datac combout (337:337:337) (353:353:353))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2220:2220:2220) (2222:2222:2222))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (479:479:479))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (235:235:235) (268:268:268))
        (PORT datad (460:460:460) (483:483:483))
        (IOPATH datac combout (337:337:337) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2237:2237:2237) (2265:2265:2265))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (549:549:549) (663:663:663))
        (PORT datab (357:357:357) (472:472:472))
        (PORT datac (509:509:509) (610:610:610))
        (PORT datad (324:324:324) (417:417:417))
        (IOPATH dataa combout (416:416:416) (444:444:444))
        (IOPATH datab combout (418:418:418) (443:443:443))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (356:356:356) (471:471:471))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (496:496:496) (541:541:541))
        (PORT datad (234:234:234) (257:257:257))
        (IOPATH dataa combout (451:451:451) (460:460:460))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2237:2237:2237) (2265:2265:2265))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (538:538:538) (641:641:641))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (334:334:334) (388:388:388))
        (PORT datad (406:406:406) (435:435:435))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2220:2220:2220) (2222:2222:2222))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (355:355:355) (469:469:469))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1751:1751:1751) (1772:1772:1772))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (361:361:361) (478:478:478))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (233:233:233) (264:264:264))
        (PORT datad (459:459:459) (483:483:483))
        (IOPATH datac combout (337:337:337) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2237:2237:2237) (2265:2265:2265))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (326:326:326) (420:420:420))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2237:2237:2237) (2265:2265:2265))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (522:522:522) (632:632:632))
        (PORT datab (355:355:355) (470:470:470))
        (PORT datac (712:712:712) (789:789:789))
        (PORT datad (486:486:486) (574:574:574))
        (IOPATH dataa combout (450:450:450) (460:460:460))
        (IOPATH datab combout (418:418:418) (423:423:423))
        (IOPATH datac combout (333:333:333) (353:353:353))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (528:528:528) (638:638:638))
        (PORT datab (515:515:515) (620:620:620))
        (PORT datac (486:486:486) (579:579:579))
        (PORT datad (503:503:503) (594:594:594))
        (IOPATH dataa combout (471:471:471) (501:501:501))
        (IOPATH datab combout (523:523:523) (536:536:536))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (361:361:361) (477:477:477))
        (PORT datab (355:355:355) (470:470:470))
        (PORT datac (493:493:493) (575:575:575))
        (PORT datad (507:507:507) (596:596:596))
        (IOPATH dataa combout (437:437:437) (501:501:501))
        (IOPATH datab combout (445:445:445) (487:487:487))
        (IOPATH datac combout (337:337:337) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (552:552:552) (667:667:667))
        (PORT datab (544:544:544) (650:650:650))
        (PORT datac (506:506:506) (603:603:603))
        (PORT datad (738:738:738) (814:814:814))
        (IOPATH dataa combout (478:478:478) (509:509:509))
        (IOPATH datab combout (480:480:480) (513:513:513))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (528:528:528) (635:635:635))
        (PORT datab (542:542:542) (652:652:652))
        (PORT datac (511:511:511) (610:610:610))
        (PORT datad (734:734:734) (814:814:814))
        (IOPATH dataa combout (454:454:454) (455:455:455))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datac combout (337:337:337) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (452:452:452) (499:499:499))
        (PORT datab (266:266:266) (313:313:313))
        (PORT datac (727:727:727) (730:730:730))
        (PORT datad (745:745:745) (754:754:754))
        (IOPATH dataa combout (416:416:416) (442:442:442))
        (IOPATH datab combout (418:418:418) (434:434:434))
        (IOPATH datac combout (333:333:333) (353:353:353))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (272:272:272) (325:325:325))
        (PORT datab (543:543:543) (654:654:654))
        (PORT datac (236:236:236) (273:273:273))
        (PORT datad (233:233:233) (257:257:257))
        (IOPATH dataa combout (416:416:416) (410:410:410))
        (IOPATH datab combout (460:460:460) (468:468:468))
        (IOPATH datac combout (333:333:333) (353:353:353))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[1\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2249:2249:2249) (2276:2276:2276))
        (PORT d (116:116:116) (136:136:136))
        (PORT ena (1206:1206:1206) (1259:1259:1259))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
      (HOLD ena (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (388:388:388) (518:518:518))
        (PORT datab (386:386:386) (512:512:512))
        (PORT datad (352:352:352) (455:455:455))
        (IOPATH dataa combout (478:478:478) (509:509:509))
        (IOPATH datab combout (480:480:480) (513:513:513))
        (IOPATH datac combout (515:515:515) (545:545:545))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[2\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2249:2249:2249) (2276:2276:2276))
        (PORT d (116:116:116) (136:136:136))
        (PORT ena (1206:1206:1206) (1259:1259:1259))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
      (HOLD ena (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (536:536:536) (644:644:644))
        (PORT datab (386:386:386) (512:512:512))
        (PORT datad (353:353:353) (455:455:455))
        (IOPATH dataa combout (454:454:454) (532:532:532))
        (IOPATH datab combout (480:480:480) (513:513:513))
        (IOPATH datac combout (515:515:515) (545:545:545))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[3\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2249:2249:2249) (2276:2276:2276))
        (PORT d (116:116:116) (136:136:136))
        (PORT ena (1206:1206:1206) (1259:1259:1259))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
      (HOLD ena (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (393:393:393) (523:523:523))
        (PORT datab (379:379:379) (503:503:503))
        (PORT datad (340:340:340) (438:438:438))
        (IOPATH dataa combout (478:478:478) (509:509:509))
        (IOPATH datab combout (480:480:480) (513:513:513))
        (IOPATH datac combout (515:515:515) (545:545:545))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[0\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2249:2249:2249) (2276:2276:2276))
        (PORT d (116:116:116) (136:136:136))
        (PORT ena (1206:1206:1206) (1259:1259:1259))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
      (HOLD ena (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (337:337:337) (455:455:455))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (234:234:234) (266:266:266))
        (PORT datad (672:672:672) (676:676:676))
        (IOPATH datac combout (337:337:337) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (358:358:358) (475:475:475))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (461:461:461))
        (IOPATH dataa combout (471:471:471) (532:532:532))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (353:353:353) (468:468:468))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (354:354:354) (468:468:468))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (354:354:354) (468:468:468))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (356:356:356) (471:471:471))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (266:266:266) (313:313:313))
        (PORT datad (672:672:672) (675:675:675))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (359:359:359) (475:475:475))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (353:353:353) (468:468:468))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (358:358:358) (475:475:475))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (353:353:353) (468:468:468))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (479:479:479))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (236:236:236) (273:273:273))
        (PORT datad (671:671:671) (675:675:675))
        (IOPATH datac combout (337:337:337) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1761:1761:1761) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (513:513:513) (615:615:615))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (384:384:384) (458:458:458))
        (PORT datac (419:419:419) (463:463:463))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datac combout (337:337:337) (353:353:353))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2243:2243:2243) (2277:2277:2277))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (510:510:510) (613:613:613))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (338:338:338) (396:396:396))
        (PORT datad (395:395:395) (422:422:422))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1759:1759:1759) (1782:1782:1782))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (517:517:517) (620:620:620))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (345:345:345) (404:404:404))
        (PORT datad (411:411:411) (443:443:443))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2243:2243:2243) (2277:2277:2277))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (359:359:359) (475:475:475))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1760:1760:1760) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (479:479:479))
        (IOPATH dataa combout (471:471:471) (532:532:532))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (235:235:235) (268:268:268))
        (PORT datad (461:461:461) (487:487:487))
        (IOPATH datac combout (337:337:337) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2246:2246:2246) (2274:2274:2274))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (359:359:359) (476:476:476))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1760:1760:1760) (1783:1783:1783))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (357:357:357) (472:472:472))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (498:498:498) (545:545:545))
        (PORT datad (235:235:235) (258:258:258))
        (IOPATH dataa combout (451:451:451) (460:460:460))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2246:2246:2246) (2274:2274:2274))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (526:526:526) (626:626:626))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (381:381:381) (455:455:455))
        (PORT datac (409:409:409) (452:452:452))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datac combout (337:337:337) (353:353:353))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2243:2243:2243) (2277:2277:2277))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (831:831:831) (915:915:915))
        (PORT datab (521:521:521) (634:634:634))
        (PORT datac (489:489:489) (579:579:579))
        (PORT datad (325:325:325) (419:419:419))
        (IOPATH dataa combout (416:416:416) (444:444:444))
        (IOPATH datab combout (418:418:418) (443:443:443))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (514:514:514) (616:616:616))
        (IOPATH datab combout (474:474:474) (531:531:531))
        (IOPATH datab cout (602:602:602) (434:434:434))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (338:338:338) (396:396:396))
        (PORT datad (389:389:389) (413:413:413))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2243:2243:2243) (2277:2277:2277))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (361:361:361) (478:478:478))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (499:499:499) (545:545:545))
        (PORT datad (234:234:234) (257:257:257))
        (IOPATH dataa combout (451:451:451) (460:460:460))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2246:2246:2246) (2274:2274:2274))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (541:541:541) (649:649:649))
        (IOPATH dataa combout (471:471:471) (532:532:532))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (378:378:378) (452:452:452))
        (PORT datac (390:390:390) (422:422:422))
        (IOPATH datab combout (502:502:502) (529:529:529))
        (IOPATH datac combout (337:337:337) (353:353:353))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2243:2243:2243) (2277:2277:2277))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (360:360:360) (476:476:476))
        (IOPATH dataa combout (491:491:491) (520:520:520))
        (IOPATH dataa cout (590:590:590) (426:426:426))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
        (IOPATH cin cout (80:80:80) (80:80:80))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2246:2246:2246) (2274:2274:2274))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (499:499:499) (584:584:584))
        (IOPATH datad combout (183:183:183) (162:162:162))
        (IOPATH cin combout (666:666:666) (638:638:638))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (345:345:345) (404:404:404))
        (PORT datad (405:405:405) (434:434:434))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2243:2243:2243) (2277:2277:2277))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (479:479:479))
        (PORT datab (542:542:542) (652:652:652))
        (PORT datac (507:507:507) (610:610:610))
        (PORT datad (327:327:327) (421:421:421))
        (IOPATH dataa combout (416:416:416) (410:410:410))
        (IOPATH datab combout (460:460:460) (468:468:468))
        (IOPATH datac combout (333:333:333) (353:353:353))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (528:528:528) (638:638:638))
        (PORT datab (539:539:539) (649:649:649))
        (PORT datac (781:781:781) (853:853:853))
        (PORT datad (486:486:486) (573:573:573))
        (IOPATH dataa combout (471:471:471) (501:501:501))
        (IOPATH datab combout (523:523:523) (536:536:536))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (361:361:361) (478:478:478))
        (PORT datab (357:357:357) (472:472:472))
        (PORT datac (324:324:324) (425:425:425))
        (PORT datad (491:491:491) (574:574:574))
        (IOPATH dataa combout (437:437:437) (501:501:501))
        (IOPATH datab combout (445:445:445) (487:487:487))
        (IOPATH datac combout (337:337:337) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (552:552:552) (666:666:666))
        (PORT datab (541:541:541) (650:650:650))
        (PORT datac (795:795:795) (861:861:861))
        (PORT datad (484:484:484) (567:567:567))
        (IOPATH dataa combout (471:471:471) (501:501:501))
        (IOPATH datab combout (523:523:523) (536:536:536))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (344:344:344) (463:463:463))
        (PORT datab (336:336:336) (454:454:454))
        (PORT datac (491:491:491) (573:573:573))
        (PORT datad (489:489:489) (564:564:564))
        (IOPATH dataa combout (478:478:478) (509:509:509))
        (IOPATH datab combout (480:480:480) (513:513:513))
        (IOPATH datac combout (333:333:333) (352:352:352))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (459:459:459) (506:506:506))
        (PORT datab (264:264:264) (309:309:309))
        (PORT datac (731:731:731) (734:734:734))
        (PORT datad (1054:1054:1054) (1037:1037:1037))
        (IOPATH dataa combout (416:416:416) (442:442:442))
        (IOPATH datab combout (418:418:418) (434:434:434))
        (IOPATH datac combout (333:333:333) (353:353:353))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (269:269:269) (316:316:316))
        (PORT datab (358:358:358) (474:474:474))
        (PORT datac (236:236:236) (272:272:272))
        (PORT datad (234:234:234) (258:258:258))
        (IOPATH dataa combout (416:416:416) (442:442:442))
        (IOPATH datab combout (418:418:418) (434:434:434))
        (IOPATH datac combout (333:333:333) (353:353:353))
        (IOPATH datad combout (183:183:183) (162:162:162))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_pin\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (384:384:384) (458:458:458))
        (IOPATH datab combout (502:502:502) (531:531:531))
        (IOPATH datac combout (515:515:515) (545:545:545))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_pin\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2243:2243:2243) (2277:2277:2277))
        (PORT d (116:116:116) (136:136:136))
        (IOPATH (posedge clk) q (310:310:310) (310:310:310))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (244:244:244))
    )
  )
)
