// Copyright (C) 2018  Intel Corporation. All rights reserved.
// Your use of Intel Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Intel Program License 
// Subscription Agreement, the Intel Quartus Prime License Agreement,
// the Intel FPGA IP License Agreement, or other applicable license
// agreement, including, without limitation, that your use is for
// the sole purpose of programming logic devices manufactured by
// Intel and sold by Intel or its authorized distributors.  Please
// refer to the applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8L Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8L,
// with speed grade 8L, core voltage 1.0VmV, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (VHDL) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "LED")
  (DATE "07/31/2025 13:23:38")
  (VENDOR "Altera")
  (PROGRAM "Quartus Prime")
  (VERSION "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[0\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1495:1495:1495) (1457:1457:1457))
        (IOPATH i o (3064:3064:3064) (3059:3059:3059))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[1\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1365:1365:1365) (1346:1346:1346))
        (IOPATH i o (2962:2962:2962) (2967:2967:2967))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[2\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1545:1545:1545) (1516:1516:1516))
        (IOPATH i o (3067:3067:3067) (3035:3035:3035))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[3\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1382:1382:1382) (1363:1363:1363))
        (IOPATH i o (3067:3067:3067) (3035:3035:3035))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\toggle_pin\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1672:1672:1672) (1672:1672:1672))
        (IOPATH i o (3064:3064:3064) (3059:3059:3059))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE \\clk\~input\\)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (844:844:844) (820:820:820))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE \\clk\~inputclkctrl\\)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (192:192:192) (188:188:188))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (380:380:380) (504:504:504))
        (PORT datab (369:369:369) (491:491:491))
        (PORT datad (327:327:327) (429:429:429))
        (IOPATH dataa combout (455:455:455) (494:494:494))
        (IOPATH datab combout (442:442:442) (518:518:518))
        (IOPATH datac combout (509:509:509) (533:533:533))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (226:226:226) (259:259:259))
        (PORT datad (796:796:796) (789:789:789))
        (IOPATH datac combout (328:328:328) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (462:462:462))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (462:462:462))
        (IOPATH dataa combout (454:454:454) (521:521:521))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (349:349:349) (464:464:464))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (256:256:256) (301:301:301))
        (PORT datad (795:795:795) (789:789:789))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (462:462:462))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (462:462:462))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (347:347:347) (461:461:461))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (257:257:257) (302:302:302))
        (PORT datad (796:796:796) (789:789:789))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (347:347:347) (461:461:461))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (228:228:228) (261:261:261))
        (PORT datad (796:796:796) (789:789:789))
        (IOPATH datac combout (328:328:328) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1651:1651:1651) (1694:1694:1694))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (502:502:502) (604:604:604))
        (IOPATH dataa combout (454:454:454) (521:521:521))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (358:358:358) (417:417:417))
        (PORT datac (400:400:400) (435:435:435))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datac combout (328:328:328) (350:350:350))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2089:2089:2089) (2101:2101:2101))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (491:491:491) (592:592:592))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (319:319:319) (366:366:366))
        (PORT datad (377:377:377) (401:401:401))
        (IOPATH datac combout (326:326:326) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2089:2089:2089) (2101:2101:2101))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1650:1650:1650) (1693:1693:1693))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (516:516:516) (621:621:621))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (321:321:321) (368:368:368))
        (PORT datad (376:376:376) (400:400:400))
        (IOPATH datac combout (326:326:326) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2089:2089:2089) (2101:2101:2101))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (457:457:457))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1650:1650:1650) (1693:1693:1693))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (498:498:498) (602:602:602))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (357:357:357) (413:413:413))
        (PORT datac (377:377:377) (408:408:408))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datac combout (328:328:328) (350:350:350))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2089:2089:2089) (2101:2101:2101))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (517:517:517) (615:615:615))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (360:360:360) (418:418:418))
        (PORT datac (397:397:397) (431:431:431))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datac combout (328:328:328) (350:350:350))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2089:2089:2089) (2101:2101:2101))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (351:351:351) (466:466:466))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (227:227:227) (259:259:259))
        (PORT datad (440:440:440) (461:461:461))
        (IOPATH datac combout (328:328:328) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2106:2106:2106) (2138:2138:2138))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (527:527:527) (636:636:636))
        (PORT datab (345:345:345) (460:460:460))
        (PORT datac (490:490:490) (588:588:588))
        (PORT datad (311:311:311) (408:408:408))
        (IOPATH dataa combout (396:396:396) (431:431:431))
        (IOPATH datab combout (400:400:400) (428:428:428))
        (IOPATH datac combout (326:326:326) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (345:345:345) (459:459:459))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (474:474:474) (516:516:516))
        (PORT datad (224:224:224) (250:250:250))
        (IOPATH dataa combout (438:438:438) (445:445:445))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2106:2106:2106) (2138:2138:2138))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (518:518:518) (614:614:614))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (324:324:324) (373:373:373))
        (PORT datad (389:389:389) (412:412:412))
        (IOPATH datac combout (326:326:326) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2089:2089:2089) (2101:2101:2101))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (343:343:343) (457:457:457))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1650:1650:1650) (1693:1693:1693))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (349:349:349) (465:465:465))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (225:225:225) (258:258:258))
        (PORT datad (439:439:439) (460:460:460))
        (IOPATH datac combout (328:328:328) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2106:2106:2106) (2138:2138:2138))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (313:313:313) (410:410:410))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2106:2106:2106) (2138:2138:2138))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (500:500:500) (607:607:607))
        (PORT datab (344:344:344) (458:458:458))
        (PORT datac (682:682:682) (752:752:752))
        (PORT datad (465:465:465) (553:553:553))
        (IOPATH dataa combout (438:438:438) (445:445:445))
        (IOPATH datab combout (400:400:400) (409:409:409))
        (IOPATH datac combout (326:326:326) (350:350:350))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (506:506:506) (614:614:614))
        (PORT datab (494:494:494) (599:599:599))
        (PORT datac (466:466:466) (560:560:560))
        (PORT datad (481:481:481) (571:571:571))
        (IOPATH dataa combout (454:454:454) (489:489:489))
        (IOPATH datab combout (502:502:502) (520:520:520))
        (IOPATH datac combout (330:330:330) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (348:348:348) (464:464:464))
        (PORT datab (343:343:343) (457:457:457))
        (PORT datac (472:472:472) (558:558:558))
        (PORT datad (486:486:486) (576:576:576))
        (IOPATH dataa combout (417:417:417) (491:491:491))
        (IOPATH datab combout (426:426:426) (474:474:474))
        (IOPATH datac combout (328:328:328) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (529:529:529) (637:637:637))
        (PORT datab (522:522:522) (626:626:626))
        (PORT datac (487:487:487) (585:585:585))
        (PORT datad (706:706:706) (772:772:772))
        (IOPATH dataa combout (455:455:455) (494:494:494))
        (IOPATH datab combout (458:458:458) (498:498:498))
        (IOPATH datac combout (330:330:330) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (506:506:506) (614:614:614))
        (PORT datab (519:519:519) (627:627:627))
        (PORT datac (492:492:492) (589:589:589))
        (PORT datad (702:702:702) (770:770:770))
        (IOPATH dataa combout (439:439:439) (444:444:444))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datac combout (328:328:328) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (431:431:431) (464:464:464))
        (PORT datab (256:256:256) (301:301:301))
        (PORT datac (696:696:696) (688:688:688))
        (PORT datad (713:713:713) (704:704:704))
        (IOPATH dataa combout (396:396:396) (430:430:430))
        (IOPATH datab combout (400:400:400) (419:419:419))
        (IOPATH datac combout (326:326:326) (350:350:350))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (261:261:261) (309:309:309))
        (PORT datab (521:521:521) (629:629:629))
        (PORT datac (228:228:228) (261:261:261))
        (PORT datad (223:223:223) (250:250:250))
        (IOPATH dataa combout (396:396:396) (395:395:395))
        (IOPATH datab combout (444:444:444) (454:454:454))
        (IOPATH datac combout (326:326:326) (350:350:350))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[1\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2117:2117:2117) (2150:2150:2150))
        (PORT d (119:119:119) (131:131:131))
        (PORT ena (1196:1196:1196) (1267:1267:1267))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
      (HOLD ena (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (374:374:374) (500:500:500))
        (PORT datab (375:375:375) (496:496:496))
        (PORT datad (340:340:340) (443:443:443))
        (IOPATH dataa combout (455:455:455) (494:494:494))
        (IOPATH datab combout (458:458:458) (498:498:498))
        (IOPATH datac combout (509:509:509) (533:533:533))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[2\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2117:2117:2117) (2150:2150:2150))
        (PORT d (119:119:119) (131:131:131))
        (PORT ena (1196:1196:1196) (1267:1267:1267))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
      (HOLD ena (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (511:511:511) (618:618:618))
        (PORT datab (375:375:375) (496:496:496))
        (PORT datad (340:340:340) (443:443:443))
        (IOPATH dataa combout (439:439:439) (521:521:521))
        (IOPATH datab combout (458:458:458) (498:498:498))
        (IOPATH datac combout (509:509:509) (533:533:533))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[3\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2117:2117:2117) (2150:2150:2150))
        (PORT d (119:119:119) (131:131:131))
        (PORT ena (1196:1196:1196) (1267:1267:1267))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
      (HOLD ena (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (379:379:379) (504:504:504))
        (PORT datab (367:367:367) (488:488:488))
        (PORT datad (327:327:327) (428:428:428))
        (IOPATH dataa combout (455:455:455) (494:494:494))
        (IOPATH datab combout (458:458:458) (498:498:498))
        (IOPATH datac combout (509:509:509) (533:533:533))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[0\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2117:2117:2117) (2150:2150:2150))
        (PORT d (119:119:119) (131:131:131))
        (PORT ena (1196:1196:1196) (1267:1267:1267))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
      (HOLD ena (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (326:326:326) (441:441:441))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (226:226:226) (260:260:260))
        (PORT datad (642:642:642) (636:636:636))
        (IOPATH datac combout (328:328:328) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (462:462:462))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (329:329:329) (447:447:447))
        (IOPATH dataa combout (454:454:454) (521:521:521))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (345:345:345) (459:459:459))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (256:256:256) (301:301:301))
        (PORT datad (642:642:642) (636:636:636))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (462:462:462))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (462:462:462))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (456:456:456))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (350:350:350) (466:466:466))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (228:228:228) (261:261:261))
        (PORT datad (641:641:641) (635:635:635))
        (IOPATH datac combout (328:328:328) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (493:493:493) (597:597:597))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (373:373:373) (439:439:439))
        (PORT datac (401:401:401) (436:436:436))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datac combout (328:328:328) (350:350:350))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2112:2112:2112) (2153:2153:2153))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (490:490:490) (592:592:592))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (330:330:330) (380:380:380))
        (PORT datad (377:377:377) (401:401:401))
        (IOPATH datac combout (326:326:326) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1657:1657:1657) (1701:1701:1701))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (496:496:496) (596:596:596))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (336:336:336) (390:390:390))
        (PORT datad (395:395:395) (421:421:421))
        (IOPATH datac combout (326:326:326) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2112:2112:2112) (2153:2153:2153))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (463:463:463))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (350:350:350) (466:466:466))
        (IOPATH dataa combout (454:454:454) (521:521:521))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (227:227:227) (260:260:260))
        (PORT datad (442:442:442) (463:463:463))
        (IOPATH datac combout (328:328:328) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2114:2114:2114) (2150:2150:2150))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (347:347:347) (463:463:463))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1658:1658:1658) (1703:1703:1703))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (346:346:346) (460:460:460))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (477:477:477) (519:519:519))
        (PORT datad (225:225:225) (251:251:251))
        (IOPATH dataa combout (438:438:438) (445:445:445))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2114:2114:2114) (2150:2150:2150))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (501:501:501) (603:603:603))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (369:369:369) (436:436:436))
        (PORT datac (393:393:393) (428:428:428))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datac combout (328:328:328) (350:350:350))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2112:2112:2112) (2153:2153:2153))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (796:796:796) (869:869:869))
        (PORT datab (500:500:500) (609:609:609))
        (PORT datac (469:469:469) (561:561:561))
        (PORT datad (313:313:313) (409:409:409))
        (IOPATH dataa combout (396:396:396) (431:431:431))
        (IOPATH datab combout (400:400:400) (428:428:428))
        (IOPATH datac combout (326:326:326) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (494:494:494) (595:595:595))
        (IOPATH datab combout (456:456:456) (518:518:518))
        (IOPATH datab cout (570:570:570) (409:409:409))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (330:330:330) (380:380:380))
        (PORT datad (373:373:373) (391:391:391))
        (IOPATH datac combout (326:326:326) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2112:2112:2112) (2153:2153:2153))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (349:349:349) (465:465:465))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (477:477:477) (519:519:519))
        (PORT datad (224:224:224) (250:250:250))
        (IOPATH dataa combout (438:438:438) (445:445:445))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2114:2114:2114) (2150:2150:2150))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (518:518:518) (622:622:622))
        (IOPATH dataa combout (454:454:454) (521:521:521))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (366:366:366) (432:432:432))
        (PORT datac (374:374:374) (404:404:404))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datac combout (328:328:328) (350:350:350))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2112:2112:2112) (2153:2153:2153))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (347:347:347) (463:463:463))
        (IOPATH dataa combout (479:479:479) (509:509:509))
        (IOPATH dataa cout (561:561:561) (404:404:404))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
        (IOPATH cin cout (75:75:75) (75:75:75))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2114:2114:2114) (2150:2150:2150))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (480:480:480) (564:564:564))
        (IOPATH datad combout (178:178:178) (153:153:153))
        (IOPATH cin combout (653:653:653) (620:620:620))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (337:337:337) (390:390:390))
        (PORT datad (387:387:387) (409:409:409))
        (IOPATH datac combout (326:326:326) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2112:2112:2112) (2153:2153:2153))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (350:350:350) (466:466:466))
        (PORT datab (518:518:518) (623:623:623))
        (PORT datac (488:488:488) (586:586:586))
        (PORT datad (315:315:315) (412:412:412))
        (IOPATH dataa combout (396:396:396) (395:395:395))
        (IOPATH datab combout (444:444:444) (454:454:454))
        (IOPATH datac combout (326:326:326) (350:350:350))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (506:506:506) (614:614:614))
        (PORT datab (516:516:516) (620:620:620))
        (PORT datac (749:749:749) (812:812:812))
        (PORT datad (465:465:465) (552:552:552))
        (IOPATH dataa combout (454:454:454) (489:489:489))
        (IOPATH datab combout (502:502:502) (520:520:520))
        (IOPATH datac combout (330:330:330) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (349:349:349) (465:465:465))
        (PORT datab (346:346:346) (460:460:460))
        (PORT datac (314:314:314) (417:417:417))
        (PORT datad (471:471:471) (557:557:557))
        (IOPATH dataa combout (417:417:417) (491:491:491))
        (IOPATH datab combout (426:426:426) (474:474:474))
        (IOPATH datac combout (328:328:328) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (529:529:529) (635:635:635))
        (PORT datab (517:517:517) (621:621:621))
        (PORT datac (762:762:762) (818:818:818))
        (PORT datad (463:463:463) (549:549:549))
        (IOPATH dataa combout (454:454:454) (489:489:489))
        (IOPATH datab combout (502:502:502) (520:520:520))
        (IOPATH datac combout (330:330:330) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (331:331:331) (449:449:449))
        (PORT datab (324:324:324) (441:441:441))
        (PORT datac (469:469:469) (555:555:555))
        (PORT datad (468:468:468) (546:546:546))
        (IOPATH dataa combout (455:455:455) (494:494:494))
        (IOPATH datab combout (458:458:458) (498:498:498))
        (IOPATH datac combout (330:330:330) (349:349:349))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (439:439:439) (475:475:475))
        (PORT datab (254:254:254) (300:300:300))
        (PORT datac (701:701:701) (692:692:692))
        (PORT datad (1007:1007:1007) (965:965:965))
        (IOPATH dataa combout (396:396:396) (430:430:430))
        (IOPATH datab combout (400:400:400) (419:419:419))
        (IOPATH datac combout (326:326:326) (350:350:350))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (258:258:258) (305:305:305))
        (PORT datab (347:347:347) (462:462:462))
        (PORT datac (228:228:228) (261:261:261))
        (PORT datad (225:225:225) (251:251:251))
        (IOPATH dataa combout (396:396:396) (430:430:430))
        (IOPATH datab combout (400:400:400) (419:419:419))
        (IOPATH datac combout (326:326:326) (350:350:350))
        (IOPATH datad combout (178:178:178) (153:153:153))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_pin\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (373:373:373) (439:439:439))
        (IOPATH datab combout (486:486:486) (519:519:519))
        (IOPATH datac combout (509:509:509) (533:533:533))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_pin\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2112:2112:2112) (2153:2153:2153))
        (PORT d (119:119:119) (131:131:131))
        (IOPATH (posedge clk) q (320:320:320) (320:320:320))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (255:255:255))
    )
  )
)
