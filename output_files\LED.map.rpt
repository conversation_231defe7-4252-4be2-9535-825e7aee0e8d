Analysis & Synthesis report for LED
Thu Jul 31 14:08:22 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Analysis & Synthesis Summary
  3. Analysis & Synthesis Settings
  4. Parallel Compilation
  5. Analysis & Synthesis Source Files Read
  6. Analysis & Synthesis Resource Usage Summary
  7. Analysis & Synthesis Resource Utilization by Entity
  8. General Register Statistics
  9. Post-Synthesis Netlist Statistics for Top Partition
 10. Elapsed Time Per Partition
 11. Analysis & Synthesis Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+----------------------------------------------------------------------------------+
; Analysis & Synthesis Summary                                                     ;
+------------------------------------+---------------------------------------------+
; Analysis & Synthesis Status        ; Successful - Thu Jul 31 14:08:22 2025       ;
; Quartus Prime Version              ; 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Revision Name                      ; LED                                         ;
; Top-level Entity Name              ; LED                                         ;
; Family                             ; Cyclone IV E                                ;
; Total logic elements               ; 97                                          ;
;     Total combinational functions  ; 97                                          ;
;     Dedicated logic registers      ; 55                                          ;
; Total registers                    ; 55                                          ;
; Total pins                         ; 6                                           ;
; Total virtual pins                 ; 0                                           ;
; Total memory bits                  ; 0                                           ;
; Embedded Multiplier 9-bit elements ; 0                                           ;
; Total PLLs                         ; 0                                           ;
+------------------------------------+---------------------------------------------+


+------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Settings                                                                              ;
+------------------------------------------------------------------+--------------------+--------------------+
; Option                                                           ; Setting            ; Default Value      ;
+------------------------------------------------------------------+--------------------+--------------------+
; Device                                                           ; EP4CE6E22C8        ;                    ;
; Top-level entity name                                            ; LED                ; LED                ;
; Family name                                                      ; Cyclone IV E       ; Cyclone IV GX      ;
; Use smart compilation                                            ; Off                ; Off                ;
; Enable parallel Assembler and Timing Analyzer during compilation ; On                 ; On                 ;
; Enable compact report table                                      ; Off                ; Off                ;
; Restructure Multiplexers                                         ; Auto               ; Auto               ;
; Create Debugging Nodes for IP Cores                              ; Off                ; Off                ;
; Preserve fewer node names                                        ; On                 ; On                 ;
; Intel FPGA IP Evaluation Mode                                    ; Enable             ; Enable             ;
; Verilog Version                                                  ; Verilog_2001       ; Verilog_2001       ;
; VHDL Version                                                     ; VHDL_1993          ; VHDL_1993          ;
; State Machine Processing                                         ; Auto               ; Auto               ;
; Safe State Machine                                               ; Off                ; Off                ;
; Extract Verilog State Machines                                   ; On                 ; On                 ;
; Extract VHDL State Machines                                      ; On                 ; On                 ;
; Ignore Verilog initial constructs                                ; Off                ; Off                ;
; Iteration limit for constant Verilog loops                       ; 5000               ; 5000               ;
; Iteration limit for non-constant Verilog loops                   ; 250                ; 250                ;
; Add Pass-Through Logic to Inferred RAMs                          ; On                 ; On                 ;
; Infer RAMs from Raw Logic                                        ; On                 ; On                 ;
; Parallel Synthesis                                               ; On                 ; On                 ;
; DSP Block Balancing                                              ; Auto               ; Auto               ;
; NOT Gate Push-Back                                               ; On                 ; On                 ;
; Power-Up Don't Care                                              ; On                 ; On                 ;
; Remove Redundant Logic Cells                                     ; Off                ; Off                ;
; Remove Duplicate Registers                                       ; On                 ; On                 ;
; Ignore CARRY Buffers                                             ; Off                ; Off                ;
; Ignore CASCADE Buffers                                           ; Off                ; Off                ;
; Ignore GLOBAL Buffers                                            ; Off                ; Off                ;
; Ignore ROW GLOBAL Buffers                                        ; Off                ; Off                ;
; Ignore LCELL Buffers                                             ; Off                ; Off                ;
; Ignore SOFT Buffers                                              ; On                 ; On                 ;
; Limit AHDL Integers to 32 Bits                                   ; Off                ; Off                ;
; Optimization Technique                                           ; Balanced           ; Balanced           ;
; Carry Chain Length                                               ; 70                 ; 70                 ;
; Auto Carry Chains                                                ; On                 ; On                 ;
; Auto Open-Drain Pins                                             ; On                 ; On                 ;
; Perform WYSIWYG Primitive Resynthesis                            ; Off                ; Off                ;
; Auto ROM Replacement                                             ; On                 ; On                 ;
; Auto RAM Replacement                                             ; On                 ; On                 ;
; Auto DSP Block Replacement                                       ; On                 ; On                 ;
; Auto Shift Register Replacement                                  ; Auto               ; Auto               ;
; Allow Shift Register Merging across Hierarchies                  ; Auto               ; Auto               ;
; Auto Clock Enable Replacement                                    ; On                 ; On                 ;
; Strict RAM Replacement                                           ; Off                ; Off                ;
; Allow Synchronous Control Signals                                ; On                 ; On                 ;
; Force Use of Synchronous Clear Signals                           ; Off                ; Off                ;
; Auto RAM Block Balancing                                         ; On                 ; On                 ;
; Auto RAM to Logic Cell Conversion                                ; Off                ; Off                ;
; Auto Resource Sharing                                            ; Off                ; Off                ;
; Allow Any RAM Size For Recognition                               ; Off                ; Off                ;
; Allow Any ROM Size For Recognition                               ; Off                ; Off                ;
; Allow Any Shift Register Size For Recognition                    ; Off                ; Off                ;
; Use LogicLock Constraints during Resource Balancing              ; On                 ; On                 ;
; Ignore translate_off and synthesis_off directives                ; Off                ; Off                ;
; Timing-Driven Synthesis                                          ; On                 ; On                 ;
; Report Parameter Settings                                        ; On                 ; On                 ;
; Report Source Assignments                                        ; On                 ; On                 ;
; Report Connectivity Checks                                       ; On                 ; On                 ;
; Ignore Maximum Fan-Out Assignments                               ; Off                ; Off                ;
; Synchronization Register Chain Length                            ; 2                  ; 2                  ;
; Power Optimization During Synthesis                              ; Normal compilation ; Normal compilation ;
; HDL message level                                                ; Level2             ; Level2             ;
; Suppress Register Optimization Related Messages                  ; Off                ; Off                ;
; Number of Removed Registers Reported in Synthesis Report         ; 5000               ; 5000               ;
; Number of Swept Nodes Reported in Synthesis Report               ; 5000               ; 5000               ;
; Number of Inverted Registers Reported in Synthesis Report        ; 100                ; 100                ;
; Clock MUX Protection                                             ; On                 ; On                 ;
; Auto Gated Clock Conversion                                      ; Off                ; Off                ;
; Block Design Naming                                              ; Auto               ; Auto               ;
; SDC constraint protection                                        ; Off                ; Off                ;
; Synthesis Effort                                                 ; Auto               ; Auto               ;
; Shift Register Replacement - Allow Asynchronous Clear Signal     ; On                 ; On                 ;
; Pre-Mapping Resynthesis Optimization                             ; Off                ; Off                ;
; Analysis & Synthesis Message Level                               ; Medium             ; Medium             ;
; Disable Register Merging Across Hierarchies                      ; Auto               ; Auto               ;
; Resource Aware Inference For Block RAM                           ; On                 ; On                 ;
+------------------------------------------------------------------+--------------------+--------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 20          ;
; Maximum allowed            ; 14          ;
;                            ;             ;
; Average used               ; 1.00        ;
; Maximum used               ; 14          ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     Processor 1            ; 100.0%      ;
;     Processors 2-14        ;   0.0%      ;
+----------------------------+-------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Source Files Read                                                                                                             ;
+----------------------------------+-----------------+------------------------+------------------------------------------------------------+---------+
; File Name with User-Entered Path ; Used in Netlist ; File Type              ; File Name with Absolute Path                               ; Library ;
+----------------------------------+-----------------+------------------------+------------------------------------------------------------+---------+
; LED.v                            ; yes             ; User Verilog HDL File  ; C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v ;         ;
+----------------------------------+-----------------+------------------------+------------------------------------------------------------+---------+


+---------------------------------------------------------+
; Analysis & Synthesis Resource Usage Summary             ;
+---------------------------------------------+-----------+
; Resource                                    ; Usage     ;
+---------------------------------------------+-----------+
; Estimated Total logic elements              ; 97        ;
;                                             ;           ;
; Total combinational functions               ; 97        ;
; Logic element usage by number of LUT inputs ;           ;
;     -- 4 input functions                    ; 20        ;
;     -- 3 input functions                    ; 0         ;
;     -- <=2 input functions                  ; 77        ;
;                                             ;           ;
; Logic elements by mode                      ;           ;
;     -- normal mode                          ; 49        ;
;     -- arithmetic mode                      ; 48        ;
;                                             ;           ;
; Total registers                             ; 55        ;
;     -- Dedicated logic registers            ; 55        ;
;     -- I/O registers                        ; 0         ;
;                                             ;           ;
; I/O pins                                    ; 6         ;
;                                             ;           ;
; Embedded Multiplier 9-bit elements          ; 0         ;
;                                             ;           ;
; Maximum fan-out node                        ; clk~input ;
; Maximum fan-out                             ; 55        ;
; Total fan-out                               ; 357       ;
; Average fan-out                             ; 2.18      ;
+---------------------------------------------+-----------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Resource Utilization by Entity                                                                                                                                                      ;
+----------------------------+---------------------+---------------------------+-------------+--------------+---------+-----------+------+--------------+---------------------+-------------+--------------+
; Compilation Hierarchy Node ; Combinational ALUTs ; Dedicated Logic Registers ; Memory Bits ; DSP Elements ; DSP 9x9 ; DSP 18x18 ; Pins ; Virtual Pins ; Full Hierarchy Name ; Entity Name ; Library Name ;
+----------------------------+---------------------+---------------------------+-------------+--------------+---------+-----------+------+--------------+---------------------+-------------+--------------+
; |LED                       ; 97 (97)             ; 55 (55)                   ; 0           ; 0            ; 0       ; 0         ; 6    ; 0            ; |LED                ; LED         ; work         ;
+----------------------------+---------------------+---------------------------+-------------+--------------+---------+-----------+------+--------------+---------------------+-------------+--------------+
Note: For table entries with two numbers listed, the numbers in parentheses indicate the number of resources of the given type used by the specific entity alone. The numbers listed outside of parentheses indicate the total resources of the given type used by the specific entity and all of its sub-entities in the hierarchy.


+------------------------------------------------------+
; General Register Statistics                          ;
+----------------------------------------------+-------+
; Statistic                                    ; Value ;
+----------------------------------------------+-------+
; Total registers                              ; 55    ;
; Number of registers using Synchronous Clear  ; 0     ;
; Number of registers using Synchronous Load   ; 0     ;
; Number of registers using Asynchronous Clear ; 0     ;
; Number of registers using Asynchronous Load  ; 0     ;
; Number of registers using Clock Enable       ; 4     ;
; Number of registers using Preset             ; 0     ;
+----------------------------------------------+-------+


+-----------------------------------------------------+
; Post-Synthesis Netlist Statistics for Top Partition ;
+-----------------------+-----------------------------+
; Type                  ; Count                       ;
+-----------------------+-----------------------------+
; boundary_port         ; 6                           ;
; cycloneiii_ff         ; 55                          ;
;     ENA               ; 4                           ;
;     plain             ; 51                          ;
; cycloneiii_lcell_comb ; 97                          ;
;     arith             ; 48                          ;
;         2 data inputs ; 48                          ;
;     normal            ; 49                          ;
;         1 data inputs ; 2                           ;
;         2 data inputs ; 27                          ;
;         4 data inputs ; 20                          ;
;                       ;                             ;
; Max LUT depth         ; 4.40                        ;
; Average LUT depth     ; 3.50                        ;
+-----------------------+-----------------------------+


+-------------------------------+
; Elapsed Time Per Partition    ;
+----------------+--------------+
; Partition Name ; Elapsed Time ;
+----------------+--------------+
; Top            ; 00:00:00     ;
+----------------+--------------+


+-------------------------------+
; Analysis & Synthesis Messages ;
+-------------------------------+
Info: *******************************************************************
Info: Running Quartus Prime Analysis & Synthesis
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Thu Jul 31 14:08:16 2025
Info: Command: quartus_map --read_settings_files=on --write_settings_files=off LED -c LED
Warning (18236): Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance.
Info (20030): Parallel compilation is enabled and will use 14 of the 14 processors detected
Info (12021): Found 1 design units, including 1 entities, in source file led.v
    Info (12023): Found entity 1: LED File: C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v Line: 1
Info (12127): Elaborating entity "LED" for the top level hierarchy
Warning (10230): Verilog HDL assignment warning at LED.v(12): truncated value with size 32 to match size of target (25) File: C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v Line: 12
Warning (10230): Verilog HDL assignment warning at LED.v(13): truncated value with size 32 to match size of target (25) File: C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v Line: 13
Warning (10230): Verilog HDL assignment warning at LED.v(21): truncated value with size 8 to match size of target (4) File: C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v Line: 21
Info (286030): Timing-Driven Synthesis is running
Info (16010): Generating hard_block partition "hard_block:auto_generated_inst"
    Info (16011): Adding 0 node(s), including 0 DDIO, 0 PLL, 0 transceiver and 0 LCELL
Info (21057): Implemented 103 device resources after synthesis - the final resource count might be different
    Info (21058): Implemented 1 input pins
    Info (21059): Implemented 5 output pins
    Info (21061): Implemented 97 logic cells
Info: Quartus Prime Analysis & Synthesis was successful. 0 errors, 4 warnings
    Info: Peak virtual memory: 4831 megabytes
    Info: Processing ended: Thu Jul 31 14:08:22 2025
    Info: Elapsed time: 00:00:06
    Info: Total CPU time (on all processors): 00:00:12


