{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1753942096844 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1753942096848 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 14:08:16 2025 " "Processing started: Thu Jul 31 14:08:16 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1753942096848 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1753942096848 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off LED -c LED " "Command: quartus_map --read_settings_files=on --write_settings_files=off LED -c LED" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1753942096848 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Analysis & Synthesis" 0 -1 1753942097024 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1753942097024 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "led.v 1 1 " "Found 1 design units, including 1 entities, in source file led.v" { { "Info" "ISGN_ENTITY_NAME" "1 LED " "Found entity 1: LED" {  } { { "LED.v" "" { Text "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1753942102053 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1753942102053 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "LED " "Elaborating entity \"LED\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1753942102064 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 25 LED.v(12) " "Verilog HDL assignment warning at LED.v(12): truncated value with size 32 to match size of target (25)" {  } { { "LED.v" "" { Text "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v" 12 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1753942102064 "|LED"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 25 LED.v(13) " "Verilog HDL assignment warning at LED.v(13): truncated value with size 32 to match size of target (25)" {  } { { "LED.v" "" { Text "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v" 13 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1753942102064 "|LED"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "8 4 LED.v(21) " "Verilog HDL assignment warning at LED.v(21): truncated value with size 8 to match size of target (4)" {  } { { "LED.v" "" { Text "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v" 21 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1753942102064 "|LED"}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Analysis & Synthesis" 0 -1 1753942102359 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "0 0 0 0 0 " "Adding 0 node(s), including 0 DDIO, 0 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Design Software" 0 -1 1753942102635 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1753942102635 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "103 " "Implemented 103 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "1 " "Implemented 1 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Design Software" 0 -1 1753942102659 ""} { "Info" "ICUT_CUT_TM_OPINS" "5 " "Implemented 5 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Design Software" 0 -1 1753942102659 ""} { "Info" "ICUT_CUT_TM_LCELLS" "97 " "Implemented 97 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Design Software" 0 -1 1753942102659 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Analysis & Synthesis" 0 -1 1753942102659 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 4 s Quartus Prime " "Quartus Prime Analysis & Synthesis was successful. 0 errors, 4 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4831 " "Peak virtual memory: 4831 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1753942102672 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 14:08:22 2025 " "Processing ended: Thu Jul 31 14:08:22 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1753942102672 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:06 " "Elapsed time: 00:00:06" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1753942102672 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:12 " "Total CPU time (on all processors): 00:00:12" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1753942102672 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1753942102672 ""}
