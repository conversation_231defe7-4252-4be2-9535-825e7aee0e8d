Timing Analyzer report for LED
Thu Jul 31 14:08:28 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Timing Analyzer Summary
  3. Parallel Compilation
  4. Clocks
  5. Slow 1200mV 85C Model Fmax Summary
  6. Timing Closure Recommendations
  7. Slow 1200mV 85C Model Setup Summary
  8. Slow 1200mV 85C Model Hold Summary
  9. Slow 1200mV 85C Model Recovery Summary
 10. Slow 1200mV 85C Model Removal Summary
 11. Slow 1200mV 85C Model Minimum Pulse Width Summary
 12. Slow 1200mV 85C Model Setup: 'clk'
 13. Slow 1200mV 85C Model Hold: 'clk'
 14. Slow 1200mV 85C Model Metastability Summary
 15. Slow 1200mV 0C Model Fmax Summary
 16. Slow 1200mV 0C Model Setup Summary
 17. Slow 1200mV 0C Model Hold Summary
 18. Slow 1200mV 0C Model Recovery Summary
 19. Slow 1200mV 0C Model Removal Summary
 20. Slow 1200mV 0C Model Minimum Pulse Width Summary
 21. Slow 1200mV 0C Model Setup: 'clk'
 22. Slow 1200mV 0C Model Hold: 'clk'
 23. Slow 1200mV 0C Model Metastability Summary
 24. Fast 1200mV 0C Model Setup Summary
 25. Fast 1200mV 0C Model Hold Summary
 26. Fast 1200mV 0C Model Recovery Summary
 27. Fast 1200mV 0C Model Removal Summary
 28. Fast 1200mV 0C Model Minimum Pulse Width Summary
 29. Fast 1200mV 0C Model Setup: 'clk'
 30. Fast 1200mV 0C Model Hold: 'clk'
 31. Fast 1200mV 0C Model Metastability Summary
 32. Multicorner Timing Analysis Summary
 33. Board Trace Model Assignments
 34. Input Transition Times
 35. Signal Integrity Metrics (Slow 1200mv 0c Model)
 36. Signal Integrity Metrics (Slow 1200mv 85c Model)
 37. Signal Integrity Metrics (Fast 1200mv 0c Model)
 38. Setup Transfers
 39. Hold Transfers
 40. Report TCCS
 41. Report RSKM
 42. Unconstrained Paths Summary
 43. Clock Status Summary
 44. Unconstrained Output Ports
 45. Unconstrained Output Ports
 46. Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+-----------------------------------------------------------------------------+
; Timing Analyzer Summary                                                     ;
+-----------------------+-----------------------------------------------------+
; Quartus Prime Version ; Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Timing Analyzer       ; Legacy Timing Analyzer                              ;
; Revision Name         ; LED                                                 ;
; Device Family         ; Cyclone IV E                                        ;
; Device Name           ; EP4CE6E22C8                                         ;
; Timing Models         ; Final                                               ;
; Delay Model           ; Combined                                            ;
; Rise/Fall Delays      ; Enabled                                             ;
+-----------------------+-----------------------------------------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 20          ;
; Maximum allowed            ; 14          ;
;                            ;             ;
; Average used               ; 1.04        ;
; Maximum used               ; 14          ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     Processor 1            ; 100.0%      ;
;     Processors 2-14        ;   0.3%      ;
+----------------------------+-------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                          ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------+
; Clock Name ; Type ; Period ; Frequency  ; Rise  ; Fall  ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master ; Source ; Targets ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------+
; clk        ; Base ; 1.000  ; 1000.0 MHz ; 0.000 ; 0.500 ;            ;           ;             ;       ;        ;           ;            ;          ;        ;        ; { clk } ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------+


+--------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary               ;
+------------+-----------------+------------+------+
; Fmax       ; Restricted Fmax ; Clock Name ; Note ;
+------------+-----------------+------------+------+
; 241.78 MHz ; 241.78 MHz      ; clk        ;      ;
+------------+-----------------+------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+-------------------------------------+
; Slow 1200mV 85C Model Setup Summary ;
+-------+--------+--------------------+
; Clock ; Slack  ; End Point TNS      ;
+-------+--------+--------------------+
; clk   ; -3.136 ; -122.630           ;
+-------+--------+--------------------+


+------------------------------------+
; Slow 1200mV 85C Model Hold Summary ;
+-------+-------+--------------------+
; Clock ; Slack ; End Point TNS      ;
+-------+-------+--------------------+
; clk   ; 0.434 ; 0.000              ;
+-------+-------+--------------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+---------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary ;
+-------+--------+----------------------------------+
; Clock ; Slack  ; End Point TNS                    ;
+-------+--------+----------------------------------+
; clk   ; -3.000 ; -84.785                          ;
+-------+--------+----------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'clk'                                                                                     ;
+--------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node          ; To Node            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; -3.136 ; counter[0]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.082     ; 4.055      ;
; -3.119 ; counter[1]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.082     ; 4.038      ;
; -3.076 ; toggle_counter[9]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.997      ;
; -3.076 ; toggle_counter[9]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.997      ;
; -3.068 ; toggle_counter[8]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.989      ;
; -3.068 ; toggle_counter[8]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.989      ;
; -3.027 ; counter[1]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.946      ;
; -3.026 ; counter[0]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.945      ;
; -3.020 ; counter[7]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.081     ; 3.940      ;
; -3.020 ; counter[7]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.081     ; 3.940      ;
; -3.012 ; counter[4]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.081     ; 3.932      ;
; -3.012 ; counter[4]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.081     ; 3.932      ;
; -3.002 ; toggle_counter[7]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.923      ;
; -3.002 ; toggle_counter[7]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.923      ;
; -2.996 ; counter[17]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.080     ; 3.917      ;
; -2.996 ; counter[17]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.080     ; 3.917      ;
; -2.989 ; counter[0]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.908      ;
; -2.988 ; counter[2]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.907      ;
; -2.979 ; counter[10]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.899      ;
; -2.978 ; counter[3]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.897      ;
; -2.974 ; toggle_counter[5]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.895      ;
; -2.974 ; toggle_counter[5]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.895      ;
; -2.930 ; toggle_counter[1]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.851      ;
; -2.930 ; toggle_counter[1]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.851      ;
; -2.929 ; counter[19]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.571     ; 3.359      ;
; -2.929 ; counter[19]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.571     ; 3.359      ;
; -2.919 ; toggle_counter[0]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.840      ;
; -2.919 ; toggle_counter[0]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.840      ;
; -2.900 ; counter[11]        ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.820      ;
; -2.889 ; counter[2]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.808      ;
; -2.886 ; counter[3]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.805      ;
; -2.880 ; counter[1]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.799      ;
; -2.875 ; toggle_counter[4]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.796      ;
; -2.875 ; toggle_counter[4]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.796      ;
; -2.867 ; toggle_counter[5]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.788      ;
; -2.867 ; toggle_counter[9]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.788      ;
; -2.866 ; toggle_counter[5]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.787      ;
; -2.866 ; toggle_counter[9]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.787      ;
; -2.866 ; toggle_counter[7]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.787      ;
; -2.865 ; toggle_counter[7]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.786      ;
; -2.859 ; toggle_counter[8]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.780      ;
; -2.858 ; toggle_counter[8]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.779      ;
; -2.855 ; toggle_counter[18] ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.575     ; 3.281      ;
; -2.855 ; toggle_counter[18] ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.575     ; 3.281      ;
; -2.845 ; counter[4]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.764      ;
; -2.841 ; counter[2]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.760      ;
; -2.835 ; counter[16]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.080     ; 3.756      ;
; -2.835 ; counter[16]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.080     ; 3.756      ;
; -2.832 ; counter[5]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.751      ;
; -2.832 ; counter[10]        ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.752      ;
; -2.830 ; counter[7]         ; counter[15]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.749      ;
; -2.830 ; counter[7]         ; counter[17]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.749      ;
; -2.830 ; counter[7]         ; counter[18]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.749      ;
; -2.830 ; toggle_counter[11] ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.751      ;
; -2.830 ; toggle_counter[11] ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.751      ;
; -2.824 ; counter[11]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.744      ;
; -2.822 ; counter[4]         ; counter[15]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.741      ;
; -2.822 ; counter[4]         ; counter[17]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.741      ;
; -2.822 ; counter[4]         ; counter[18]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.741      ;
; -2.819 ; toggle_counter[1]  ; toggle_counter[24] ; clk          ; clk         ; 1.000        ; 0.395      ; 4.215      ;
; -2.806 ; counter[17]        ; counter[15]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.726      ;
; -2.806 ; counter[17]        ; counter[17]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.726      ;
; -2.806 ; counter[17]        ; counter[18]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.726      ;
; -2.805 ; toggle_counter[6]  ; toggle_counter[24] ; clk          ; clk         ; 1.000        ; 0.394      ; 4.200      ;
; -2.792 ; counter[7]         ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.711      ;
; -2.788 ; toggle_counter[1]  ; toggle_counter[22] ; clk          ; clk         ; 1.000        ; 0.395      ; 4.184      ;
; -2.788 ; counter[7]         ; counter[10]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.707      ;
; -2.788 ; counter[7]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.707      ;
; -2.788 ; counter[7]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.707      ;
; -2.784 ; counter[4]         ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.703      ;
; -2.781 ; counter[0]         ; counter[19]        ; clk          ; clk         ; 1.000        ; 0.390      ; 4.172      ;
; -2.781 ; counter[10]        ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.701      ;
; -2.780 ; counter[4]         ; counter[10]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.699      ;
; -2.780 ; counter[4]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.699      ;
; -2.774 ; counter[5]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.081     ; 3.694      ;
; -2.774 ; counter[5]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.081     ; 3.694      ;
; -2.774 ; toggle_counter[6]  ; toggle_counter[22] ; clk          ; clk         ; 1.000        ; 0.394      ; 4.169      ;
; -2.773 ; counter[2]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.081     ; 3.693      ;
; -2.773 ; counter[2]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.081     ; 3.693      ;
; -2.768 ; toggle_counter[4]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.689      ;
; -2.768 ; counter[17]        ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.688      ;
; -2.767 ; toggle_counter[4]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.688      ;
; -2.766 ; counter[19]        ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.572     ; 3.195      ;
; -2.764 ; counter[18]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.080     ; 3.685      ;
; -2.764 ; counter[18]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.080     ; 3.685      ;
; -2.764 ; counter[17]        ; counter[10]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.684      ;
; -2.764 ; counter[17]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.684      ;
; -2.764 ; counter[17]        ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.081     ; 3.684      ;
; -2.762 ; counter[0]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.081     ; 3.682      ;
; -2.762 ; counter[0]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.081     ; 3.682      ;
; -2.751 ; toggle_counter[10] ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.080     ; 3.672      ;
; -2.751 ; toggle_counter[10] ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.080     ; 3.672      ;
; -2.741 ; counter[19]        ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.572     ; 3.170      ;
; -2.740 ; counter[5]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.659      ;
; -2.739 ; counter[3]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.082     ; 3.658      ;
; -2.739 ; counter[19]        ; counter[15]        ; clk          ; clk         ; 1.000        ; -0.572     ; 3.168      ;
; -2.739 ; counter[19]        ; counter[17]        ; clk          ; clk         ; 1.000        ; -0.572     ; 3.168      ;
; -2.739 ; counter[19]        ; counter[18]        ; clk          ; clk         ; 1.000        ; -0.572     ; 3.168      ;
; -2.737 ; counter[19]        ; counter[10]        ; clk          ; clk         ; 1.000        ; -0.572     ; 3.166      ;
; -2.737 ; counter[19]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.572     ; 3.166      ;
+--------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+


+-----------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'clk'                                                                                     ;
+-------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node          ; To Node            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; 0.434 ; toggle_pin~reg0    ; toggle_pin~reg0    ; clk          ; clk         ; 0.000        ; 0.100      ; 0.746      ;
; 0.446 ; led[0]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 0.758      ;
; 0.446 ; led[2]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 0.758      ;
; 0.446 ; led[3]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 0.758      ;
; 0.446 ; led[1]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 0.758      ;
; 0.507 ; led[0]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 0.819      ;
; 0.507 ; led[1]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 0.819      ;
; 0.508 ; led[1]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 0.820      ;
; 0.509 ; counter[24]        ; counter[24]        ; clk          ; clk         ; 0.000        ; 0.081      ; 0.802      ;
; 0.524 ; led[0]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 0.836      ;
; 0.742 ; toggle_counter[23] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.101      ; 1.055      ;
; 0.743 ; toggle_counter[9]  ; toggle_counter[9]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.035      ;
; 0.743 ; counter[7]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.036      ;
; 0.744 ; counter[1]         ; counter[1]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.037      ;
; 0.745 ; toggle_counter[1]  ; toggle_counter[1]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.037      ;
; 0.745 ; counter[3]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.038      ;
; 0.746 ; toggle_counter[10] ; toggle_counter[10] ; clk          ; clk         ; 0.000        ; 0.080      ; 1.038      ;
; 0.746 ; toggle_counter[8]  ; toggle_counter[8]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.038      ;
; 0.746 ; toggle_counter[3]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.038      ;
; 0.746 ; counter[2]         ; counter[2]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.039      ;
; 0.747 ; toggle_counter[2]  ; toggle_counter[2]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.039      ;
; 0.747 ; counter[6]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.040      ;
; 0.747 ; counter[4]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.040      ;
; 0.760 ; counter[9]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.053      ;
; 0.761 ; toggle_counter[15] ; toggle_counter[15] ; clk          ; clk         ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; toggle_counter[17] ; toggle_counter[17] ; clk          ; clk         ; 0.000        ; 0.081      ; 1.054      ;
; 0.762 ; toggle_counter[7]  ; toggle_counter[7]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.054      ;
; 0.762 ; led[1]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 1.074      ;
; 0.762 ; led[2]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 1.074      ;
; 0.763 ; toggle_counter[5]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.055      ;
; 0.764 ; counter[14]        ; counter[14]        ; clk          ; clk         ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; counter[8]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.057      ;
; 0.765 ; counter[22]        ; counter[22]        ; clk          ; clk         ; 0.000        ; 0.081      ; 1.058      ;
; 0.765 ; counter[16]        ; counter[16]        ; clk          ; clk         ; 0.000        ; 0.081      ; 1.058      ;
; 0.766 ; toggle_counter[4]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.058      ;
; 0.865 ; led[2]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 1.177      ;
; 0.865 ; led[3]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 1.177      ;
; 0.865 ; led[3]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 1.177      ;
; 0.865 ; led[2]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 1.177      ;
; 0.865 ; led[3]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 1.177      ;
; 0.866 ; led[0]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.100      ; 1.178      ;
; 1.021 ; toggle_counter[17] ; toggle_counter[18] ; clk          ; clk         ; 0.000        ; 0.576      ; 1.809      ;
; 1.032 ; toggle_counter[17] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.576      ; 1.820      ;
; 1.098 ; toggle_counter[9]  ; toggle_counter[10] ; clk          ; clk         ; 0.000        ; 0.080      ; 1.390      ;
; 1.098 ; counter[7]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.391      ;
; 1.099 ; counter[1]         ; counter[2]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.392      ;
; 1.099 ; counter[3]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.392      ;
; 1.100 ; toggle_counter[1]  ; toggle_counter[2]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.392      ;
; 1.100 ; counter[5]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.393      ;
; 1.100 ; toggle_counter[3]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.392      ;
; 1.107 ; toggle_counter[8]  ; toggle_counter[9]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.399      ;
; 1.107 ; counter[0]         ; counter[1]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.400      ;
; 1.107 ; counter[2]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.400      ;
; 1.108 ; counter[6]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.401      ;
; 1.108 ; toggle_counter[0]  ; toggle_counter[1]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.400      ;
; 1.108 ; toggle_counter[2]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.400      ;
; 1.116 ; toggle_counter[8]  ; toggle_counter[10] ; clk          ; clk         ; 0.000        ; 0.080      ; 1.408      ;
; 1.116 ; counter[0]         ; counter[2]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.409      ;
; 1.116 ; counter[2]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.409      ;
; 1.117 ; toggle_counter[7]  ; toggle_counter[8]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.409      ;
; 1.117 ; counter[6]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.410      ;
; 1.117 ; toggle_counter[0]  ; toggle_counter[2]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.409      ;
; 1.117 ; counter[4]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.410      ;
; 1.117 ; toggle_counter[2]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.409      ;
; 1.118 ; counter[15]        ; counter[16]        ; clk          ; clk         ; 0.000        ; 0.081      ; 1.411      ;
; 1.125 ; counter[8]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.418      ;
; 1.127 ; toggle_counter[4]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.419      ;
; 1.134 ; counter[14]        ; counter[16]        ; clk          ; clk         ; 0.000        ; 0.081      ; 1.427      ;
; 1.135 ; counter[22]        ; counter[24]        ; clk          ; clk         ; 0.000        ; 0.081      ; 1.428      ;
; 1.139 ; toggle_counter[21] ; toggle_counter[21] ; clk          ; clk         ; 0.000        ; 0.101      ; 1.452      ;
; 1.146 ; toggle_counter[18] ; toggle_counter[18] ; clk          ; clk         ; 0.000        ; 0.101      ; 1.459      ;
; 1.161 ; toggle_counter[15] ; toggle_counter[18] ; clk          ; clk         ; 0.000        ; 0.576      ; 1.949      ;
; 1.163 ; counter[18]        ; counter[18]        ; clk          ; clk         ; 0.000        ; 0.081      ; 1.456      ;
; 1.164 ; toggle_counter[15] ; toggle_counter[16] ; clk          ; clk         ; 0.000        ; 0.576      ; 1.952      ;
; 1.164 ; counter[15]        ; counter[15]        ; clk          ; clk         ; 0.000        ; 0.081      ; 1.457      ;
; 1.164 ; counter[17]        ; counter[17]        ; clk          ; clk         ; 0.000        ; 0.081      ; 1.457      ;
; 1.172 ; toggle_counter[15] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.576      ; 1.960      ;
; 1.215 ; toggle_counter[24] ; toggle_counter[22] ; clk          ; clk         ; 0.000        ; 0.100      ; 1.527      ;
; 1.216 ; toggle_counter[24] ; toggle_counter[19] ; clk          ; clk         ; 0.000        ; 0.100      ; 1.528      ;
; 1.219 ; toggle_counter[24] ; toggle_pin~reg0    ; clk          ; clk         ; 0.000        ; 0.100      ; 1.531      ;
; 1.227 ; toggle_counter[21] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.101      ; 1.540      ;
; 1.229 ; counter[7]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.522      ;
; 1.230 ; counter[1]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.523      ;
; 1.231 ; counter[5]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.524      ;
; 1.231 ; toggle_counter[1]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.523      ;
; 1.231 ; toggle_counter[3]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.523      ;
; 1.239 ; counter[1]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.532      ;
; 1.239 ; counter[3]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.532      ;
; 1.240 ; counter[5]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.533      ;
; 1.240 ; toggle_counter[1]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.532      ;
; 1.247 ; toggle_counter[15] ; toggle_counter[17] ; clk          ; clk         ; 0.000        ; 0.081      ; 1.540      ;
; 1.247 ; counter[0]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.540      ;
; 1.248 ; toggle_counter[5]  ; toggle_counter[7]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.540      ;
; 1.248 ; toggle_counter[7]  ; toggle_counter[9]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.540      ;
; 1.248 ; counter[6]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.541      ;
; 1.248 ; counter[4]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.541      ;
; 1.248 ; toggle_counter[0]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.540      ;
; 1.248 ; toggle_counter[2]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.080      ; 1.540      ;
; 1.256 ; counter[0]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.549      ;
; 1.256 ; counter[2]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.081      ; 1.549      ;
+-------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+


-----------------------------------------------
; Slow 1200mV 85C Model Metastability Summary ;
-----------------------------------------------
No synchronizer chains to report.


+-----------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                                                                         ;
+------------+-----------------+------------+---------------------------------------------------------------+
; Fmax       ; Restricted Fmax ; Clock Name ; Note                                                          ;
+------------+-----------------+------------+---------------------------------------------------------------+
; 259.47 MHz ; 250.0 MHz       ; clk        ; limit due to minimum period restriction (max I/O toggle rate) ;
+------------+-----------------+------------+---------------------------------------------------------------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+------------------------------------+
; Slow 1200mV 0C Model Setup Summary ;
+-------+--------+-------------------+
; Clock ; Slack  ; End Point TNS     ;
+-------+--------+-------------------+
; clk   ; -2.854 ; -108.785          ;
+-------+--------+-------------------+


+-----------------------------------+
; Slow 1200mV 0C Model Hold Summary ;
+-------+-------+-------------------+
; Clock ; Slack ; End Point TNS     ;
+-------+-------+-------------------+
; clk   ; 0.383 ; 0.000             ;
+-------+-------+-------------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary ;
+-------+--------+---------------------------------+
; Clock ; Slack  ; End Point TNS                   ;
+-------+--------+---------------------------------+
; clk   ; -3.000 ; -84.785                         ;
+-------+--------+---------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'clk'                                                                                      ;
+--------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node          ; To Node            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; -2.854 ; toggle_counter[9]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.072     ; 3.784      ;
; -2.854 ; toggle_counter[9]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.072     ; 3.784      ;
; -2.845 ; toggle_counter[8]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.072     ; 3.775      ;
; -2.845 ; toggle_counter[8]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.072     ; 3.775      ;
; -2.809 ; counter[17]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.071     ; 3.740      ;
; -2.809 ; counter[17]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.071     ; 3.740      ;
; -2.780 ; counter[7]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.710      ;
; -2.780 ; counter[7]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.710      ;
; -2.772 ; counter[4]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.702      ;
; -2.772 ; counter[4]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.702      ;
; -2.734 ; counter[0]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.662      ;
; -2.714 ; counter[19]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.528     ; 3.188      ;
; -2.714 ; counter[19]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.528     ; 3.188      ;
; -2.703 ; counter[1]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.631      ;
; -2.700 ; toggle_counter[7]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.072     ; 3.630      ;
; -2.700 ; toggle_counter[7]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.072     ; 3.630      ;
; -2.692 ; toggle_counter[1]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.072     ; 3.622      ;
; -2.692 ; toggle_counter[1]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.072     ; 3.622      ;
; -2.683 ; toggle_counter[0]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.072     ; 3.613      ;
; -2.683 ; toggle_counter[0]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.072     ; 3.613      ;
; -2.670 ; counter[10]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.599      ;
; -2.655 ; toggle_counter[5]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.072     ; 3.585      ;
; -2.655 ; toggle_counter[5]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.072     ; 3.585      ;
; -2.655 ; toggle_counter[11] ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.072     ; 3.585      ;
; -2.655 ; toggle_counter[11] ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.072     ; 3.585      ;
; -2.650 ; toggle_counter[18] ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.538     ; 3.114      ;
; -2.650 ; toggle_counter[18] ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.538     ; 3.114      ;
; -2.634 ; toggle_counter[9]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.075     ; 3.561      ;
; -2.634 ; toggle_counter[9]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.075     ; 3.561      ;
; -2.625 ; toggle_counter[8]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.075     ; 3.552      ;
; -2.625 ; toggle_counter[8]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.075     ; 3.552      ;
; -2.617 ; counter[0]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.545      ;
; -2.615 ; counter[17]        ; counter[15]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.544      ;
; -2.615 ; counter[17]        ; counter[17]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.544      ;
; -2.614 ; counter[17]        ; counter[18]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.543      ;
; -2.612 ; counter[1]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.540      ;
; -2.607 ; counter[0]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.535      ;
; -2.605 ; counter[2]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.533      ;
; -2.601 ; counter[18]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.071     ; 3.532      ;
; -2.601 ; counter[18]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.071     ; 3.532      ;
; -2.594 ; counter[16]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.071     ; 3.525      ;
; -2.594 ; counter[16]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.071     ; 3.525      ;
; -2.586 ; counter[7]         ; counter[15]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.514      ;
; -2.586 ; counter[7]         ; counter[17]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.514      ;
; -2.585 ; counter[7]         ; counter[18]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.513      ;
; -2.582 ; counter[5]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.512      ;
; -2.582 ; counter[5]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.512      ;
; -2.582 ; counter[3]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.510      ;
; -2.578 ; counter[4]         ; counter[15]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.506      ;
; -2.578 ; counter[4]         ; counter[17]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.506      ;
; -2.577 ; counter[4]         ; counter[18]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.505      ;
; -2.572 ; toggle_counter[4]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.072     ; 3.502      ;
; -2.572 ; toggle_counter[4]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.072     ; 3.502      ;
; -2.567 ; counter[17]        ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.496      ;
; -2.564 ; counter[17]        ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.493      ;
; -2.564 ; toggle_counter[10] ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.072     ; 3.494      ;
; -2.564 ; toggle_counter[10] ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.072     ; 3.494      ;
; -2.563 ; counter[17]        ; counter[10]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.492      ;
; -2.563 ; counter[17]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.492      ;
; -2.558 ; toggle_counter[5]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.075     ; 3.485      ;
; -2.558 ; toggle_counter[5]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.075     ; 3.485      ;
; -2.548 ; counter[2]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.478      ;
; -2.548 ; counter[2]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.478      ;
; -2.546 ; toggle_counter[7]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.075     ; 3.473      ;
; -2.546 ; toggle_counter[7]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.075     ; 3.473      ;
; -2.543 ; counter[10]        ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.472      ;
; -2.539 ; counter[0]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.469      ;
; -2.539 ; counter[0]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.469      ;
; -2.538 ; counter[11]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.467      ;
; -2.538 ; counter[7]         ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.466      ;
; -2.535 ; counter[7]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.463      ;
; -2.534 ; counter[7]         ; counter[10]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.462      ;
; -2.534 ; counter[7]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.462      ;
; -2.530 ; counter[4]         ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.458      ;
; -2.527 ; counter[4]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.455      ;
; -2.526 ; counter[4]         ; counter[10]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.454      ;
; -2.526 ; counter[4]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.454      ;
; -2.524 ; counter[11]        ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.453      ;
; -2.520 ; counter[19]        ; counter[15]        ; clk          ; clk         ; 1.000        ; -0.530     ; 2.992      ;
; -2.520 ; counter[19]        ; counter[17]        ; clk          ; clk         ; 1.000        ; -0.530     ; 2.992      ;
; -2.519 ; counter[19]        ; counter[18]        ; clk          ; clk         ; 1.000        ; -0.530     ; 2.991      ;
; -2.516 ; toggle_counter[3]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.072     ; 3.446      ;
; -2.516 ; toggle_counter[3]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.072     ; 3.446      ;
; -2.515 ; counter[17]        ; led[0]~reg0        ; clk          ; clk         ; 1.000        ; 0.372      ; 3.889      ;
; -2.515 ; counter[17]        ; led[2]~reg0        ; clk          ; clk         ; 1.000        ; 0.372      ; 3.889      ;
; -2.515 ; counter[17]        ; led[3]~reg0        ; clk          ; clk         ; 1.000        ; 0.372      ; 3.889      ;
; -2.515 ; counter[17]        ; led[1]~reg0        ; clk          ; clk         ; 1.000        ; 0.372      ; 3.889      ;
; -2.501 ; counter[2]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.429      ;
; -2.491 ; counter[3]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.419      ;
; -2.490 ; counter[6]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.420      ;
; -2.490 ; counter[6]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.072     ; 3.420      ;
; -2.489 ; counter[17]        ; counter[19]        ; clk          ; clk         ; 1.000        ; 0.366      ; 3.857      ;
; -2.489 ; toggle_counter[16] ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.538     ; 2.953      ;
; -2.489 ; toggle_counter[16] ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.538     ; 2.953      ;
; -2.489 ; counter[10]        ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.073     ; 3.418      ;
; -2.486 ; counter[7]         ; led[0]~reg0        ; clk          ; clk         ; 1.000        ; 0.371      ; 3.859      ;
; -2.486 ; counter[7]         ; led[2]~reg0        ; clk          ; clk         ; 1.000        ; 0.371      ; 3.859      ;
; -2.486 ; counter[7]         ; led[3]~reg0        ; clk          ; clk         ; 1.000        ; 0.371      ; 3.859      ;
; -2.486 ; counter[7]         ; led[1]~reg0        ; clk          ; clk         ; 1.000        ; 0.371      ; 3.859      ;
; -2.485 ; counter[1]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.074     ; 3.413      ;
+--------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+


+-----------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'clk'                                                                                      ;
+-------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node          ; To Node            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; 0.383 ; toggle_pin~reg0    ; toggle_pin~reg0    ; clk          ; clk         ; 0.000        ; 0.091      ; 0.669      ;
; 0.398 ; led[0]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 0.684      ;
; 0.398 ; led[2]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 0.684      ;
; 0.398 ; led[3]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 0.684      ;
; 0.398 ; led[1]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 0.684      ;
; 0.467 ; led[0]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 0.753      ;
; 0.467 ; led[1]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 0.753      ;
; 0.468 ; led[1]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 0.754      ;
; 0.469 ; counter[24]        ; counter[24]        ; clk          ; clk         ; 0.000        ; 0.073      ; 0.737      ;
; 0.489 ; led[0]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 0.775      ;
; 0.687 ; toggle_counter[23] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.091      ; 0.973      ;
; 0.691 ; toggle_counter[9]  ; toggle_counter[9]  ; clk          ; clk         ; 0.000        ; 0.072      ; 0.958      ;
; 0.691 ; counter[1]         ; counter[1]         ; clk          ; clk         ; 0.000        ; 0.072      ; 0.958      ;
; 0.692 ; counter[7]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.072      ; 0.959      ;
; 0.693 ; toggle_counter[2]  ; toggle_counter[2]  ; clk          ; clk         ; 0.000        ; 0.072      ; 0.960      ;
; 0.693 ; toggle_counter[1]  ; toggle_counter[1]  ; clk          ; clk         ; 0.000        ; 0.072      ; 0.960      ;
; 0.695 ; toggle_counter[10] ; toggle_counter[10] ; clk          ; clk         ; 0.000        ; 0.072      ; 0.962      ;
; 0.695 ; toggle_counter[3]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.072      ; 0.962      ;
; 0.695 ; counter[3]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.072      ; 0.962      ;
; 0.695 ; counter[2]         ; counter[2]         ; clk          ; clk         ; 0.000        ; 0.072      ; 0.962      ;
; 0.696 ; toggle_counter[8]  ; toggle_counter[8]  ; clk          ; clk         ; 0.000        ; 0.072      ; 0.963      ;
; 0.696 ; counter[6]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.072      ; 0.963      ;
; 0.698 ; counter[4]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.072      ; 0.965      ;
; 0.704 ; toggle_counter[15] ; toggle_counter[15] ; clk          ; clk         ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; led[1]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 0.990      ;
; 0.705 ; toggle_counter[17] ; toggle_counter[17] ; clk          ; clk         ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; toggle_counter[7]  ; toggle_counter[7]  ; clk          ; clk         ; 0.000        ; 0.072      ; 0.972      ;
; 0.705 ; counter[9]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.072      ; 0.972      ;
; 0.708 ; toggle_counter[5]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.072      ; 0.975      ;
; 0.708 ; led[2]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 0.994      ;
; 0.710 ; counter[14]        ; counter[14]        ; clk          ; clk         ; 0.000        ; 0.073      ; 0.978      ;
; 0.710 ; counter[16]        ; counter[16]        ; clk          ; clk         ; 0.000        ; 0.073      ; 0.978      ;
; 0.711 ; toggle_counter[4]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.072      ; 0.978      ;
; 0.711 ; counter[22]        ; counter[22]        ; clk          ; clk         ; 0.000        ; 0.073      ; 0.979      ;
; 0.711 ; counter[8]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.072      ; 0.978      ;
; 0.804 ; led[0]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 1.090      ;
; 0.806 ; led[3]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 1.092      ;
; 0.807 ; led[2]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 1.093      ;
; 0.807 ; led[3]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 1.093      ;
; 0.807 ; led[3]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 1.093      ;
; 0.807 ; led[2]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.091      ; 1.093      ;
; 0.897 ; toggle_counter[17] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.540      ; 1.632      ;
; 0.918 ; toggle_counter[17] ; toggle_counter[18] ; clk          ; clk         ; 0.000        ; 0.540      ; 1.653      ;
; 1.012 ; toggle_counter[2]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.279      ;
; 1.013 ; toggle_counter[9]  ; toggle_counter[10] ; clk          ; clk         ; 0.000        ; 0.072      ; 1.280      ;
; 1.013 ; counter[1]         ; counter[2]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.280      ;
; 1.014 ; counter[7]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.281      ;
; 1.014 ; counter[2]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.281      ;
; 1.015 ; toggle_counter[1]  ; toggle_counter[2]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.282      ;
; 1.015 ; toggle_counter[8]  ; toggle_counter[9]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.282      ;
; 1.015 ; counter[6]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.282      ;
; 1.016 ; counter[0]         ; counter[1]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.283      ;
; 1.016 ; toggle_counter[0]  ; toggle_counter[1]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.283      ;
; 1.019 ; counter[3]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.286      ;
; 1.019 ; toggle_counter[3]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.286      ;
; 1.019 ; toggle_counter[15] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.540      ; 1.754      ;
; 1.020 ; counter[5]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.287      ;
; 1.027 ; toggle_counter[7]  ; toggle_counter[8]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.294      ;
; 1.027 ; toggle_counter[2]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.294      ;
; 1.029 ; counter[8]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.296      ;
; 1.029 ; toggle_counter[4]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.296      ;
; 1.029 ; counter[15]        ; counter[16]        ; clk          ; clk         ; 0.000        ; 0.073      ; 1.297      ;
; 1.029 ; counter[2]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.296      ;
; 1.030 ; toggle_counter[8]  ; toggle_counter[10] ; clk          ; clk         ; 0.000        ; 0.072      ; 1.297      ;
; 1.030 ; counter[6]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.297      ;
; 1.031 ; counter[0]         ; counter[2]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.298      ;
; 1.031 ; toggle_counter[0]  ; toggle_counter[2]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.298      ;
; 1.032 ; counter[4]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.299      ;
; 1.039 ; toggle_counter[15] ; toggle_counter[18] ; clk          ; clk         ; 0.000        ; 0.540      ; 1.774      ;
; 1.044 ; counter[14]        ; counter[16]        ; clk          ; clk         ; 0.000        ; 0.073      ; 1.312      ;
; 1.045 ; counter[22]        ; counter[24]        ; clk          ; clk         ; 0.000        ; 0.073      ; 1.313      ;
; 1.051 ; toggle_counter[21] ; toggle_counter[21] ; clk          ; clk         ; 0.000        ; 0.091      ; 1.337      ;
; 1.057 ; toggle_counter[18] ; toggle_counter[18] ; clk          ; clk         ; 0.000        ; 0.091      ; 1.343      ;
; 1.069 ; toggle_counter[15] ; toggle_counter[16] ; clk          ; clk         ; 0.000        ; 0.540      ; 1.804      ;
; 1.074 ; counter[15]        ; counter[15]        ; clk          ; clk         ; 0.000        ; 0.073      ; 1.342      ;
; 1.077 ; counter[18]        ; counter[18]        ; clk          ; clk         ; 0.000        ; 0.073      ; 1.345      ;
; 1.079 ; counter[17]        ; counter[17]        ; clk          ; clk         ; 0.000        ; 0.073      ; 1.347      ;
; 1.102 ; toggle_counter[21] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.091      ; 1.388      ;
; 1.108 ; counter[1]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.375      ;
; 1.110 ; counter[7]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.377      ;
; 1.112 ; toggle_counter[1]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.379      ;
; 1.116 ; counter[5]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.383      ;
; 1.116 ; toggle_counter[3]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.383      ;
; 1.120 ; toggle_counter[15] ; toggle_counter[17] ; clk          ; clk         ; 0.000        ; 0.073      ; 1.388      ;
; 1.121 ; toggle_counter[7]  ; toggle_counter[9]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.388      ;
; 1.127 ; toggle_counter[24] ; toggle_counter[24] ; clk          ; clk         ; 0.000        ; 0.091      ; 1.413      ;
; 1.127 ; toggle_counter[5]  ; toggle_counter[7]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.394      ;
; 1.130 ; toggle_counter[17] ; toggle_counter[21] ; clk          ; clk         ; 0.000        ; 0.540      ; 1.865      ;
; 1.134 ; toggle_counter[24] ; toggle_counter[22] ; clk          ; clk         ; 0.000        ; 0.091      ; 1.420      ;
; 1.134 ; toggle_counter[2]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.401      ;
; 1.135 ; toggle_counter[24] ; toggle_counter[19] ; clk          ; clk         ; 0.000        ; 0.091      ; 1.421      ;
; 1.135 ; counter[1]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.402      ;
; 1.137 ; toggle_counter[1]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.404      ;
; 1.137 ; counter[6]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.404      ;
; 1.138 ; toggle_counter[24] ; toggle_pin~reg0    ; clk          ; clk         ; 0.000        ; 0.091      ; 1.424      ;
; 1.138 ; counter[0]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.405      ;
; 1.138 ; toggle_counter[0]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.072      ; 1.405      ;
; 1.139 ; counter[4]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.406      ;
; 1.141 ; counter[3]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.408      ;
; 1.142 ; counter[5]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.072      ; 1.409      ;
+-------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+


----------------------------------------------
; Slow 1200mV 0C Model Metastability Summary ;
----------------------------------------------
No synchronizer chains to report.


+------------------------------------+
; Fast 1200mV 0C Model Setup Summary ;
+-------+--------+-------------------+
; Clock ; Slack  ; End Point TNS     ;
+-------+--------+-------------------+
; clk   ; -0.809 ; -23.249           ;
+-------+--------+-------------------+


+-----------------------------------+
; Fast 1200mV 0C Model Hold Summary ;
+-------+-------+-------------------+
; Clock ; Slack ; End Point TNS     ;
+-------+-------+-------------------+
; clk   ; 0.179 ; 0.000             ;
+-------+-------+-------------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary ;
+-------+--------+---------------------------------+
; Clock ; Slack  ; End Point TNS                   ;
+-------+--------+---------------------------------+
; clk   ; -3.000 ; -61.861                         ;
+-------+--------+---------------------------------+


+------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'clk'                                                                                      ;
+--------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node          ; To Node            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; -0.809 ; counter[1]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.759      ;
; -0.809 ; counter[1]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.759      ;
; -0.799 ; counter[0]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.749      ;
; -0.784 ; counter[17]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.036     ; 1.735      ;
; -0.784 ; counter[17]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.036     ; 1.735      ;
; -0.767 ; counter[0]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.717      ;
; -0.746 ; counter[3]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.696      ;
; -0.746 ; counter[3]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.696      ;
; -0.740 ; counter[1]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.690      ;
; -0.736 ; counter[19]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.232     ; 1.491      ;
; -0.736 ; counter[19]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.232     ; 1.491      ;
; -0.731 ; counter[2]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.681      ;
; -0.730 ; counter[0]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.680      ;
; -0.725 ; counter[11]        ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.036     ; 1.676      ;
; -0.725 ; counter[11]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.036     ; 1.676      ;
; -0.709 ; counter[17]        ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.036     ; 1.660      ;
; -0.707 ; toggle_counter[18] ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.234     ; 1.460      ;
; -0.707 ; toggle_counter[18] ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.234     ; 1.460      ;
; -0.705 ; counter[17]        ; counter[10]        ; clk          ; clk         ; 1.000        ; -0.036     ; 1.656      ;
; -0.705 ; counter[17]        ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.036     ; 1.656      ;
; -0.705 ; counter[17]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.036     ; 1.656      ;
; -0.704 ; toggle_counter[6]  ; toggle_counter[24] ; clk          ; clk         ; 1.000        ; 0.155      ; 1.846      ;
; -0.704 ; toggle_counter[7]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.036     ; 1.655      ;
; -0.704 ; toggle_counter[7]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.036     ; 1.655      ;
; -0.700 ; toggle_counter[1]  ; toggle_counter[24] ; clk          ; clk         ; 1.000        ; 0.153      ; 1.840      ;
; -0.698 ; counter[4]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.037     ; 1.648      ;
; -0.698 ; counter[4]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.037     ; 1.648      ;
; -0.698 ; toggle_counter[5]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.036     ; 1.649      ;
; -0.698 ; toggle_counter[5]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.036     ; 1.649      ;
; -0.697 ; toggle_counter[6]  ; toggle_counter[22] ; clk          ; clk         ; 1.000        ; 0.155      ; 1.839      ;
; -0.697 ; counter[2]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.647      ;
; -0.696 ; counter[7]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.037     ; 1.646      ;
; -0.696 ; counter[7]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.037     ; 1.646      ;
; -0.695 ; counter[17]        ; counter[15]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.645      ;
; -0.695 ; counter[17]        ; counter[17]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.645      ;
; -0.694 ; counter[17]        ; counter[18]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.644      ;
; -0.693 ; toggle_counter[1]  ; toggle_counter[22] ; clk          ; clk         ; 1.000        ; 0.153      ; 1.833      ;
; -0.688 ; toggle_counter[8]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.036     ; 1.639      ;
; -0.688 ; toggle_counter[8]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.036     ; 1.639      ;
; -0.686 ; toggle_counter[9]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.036     ; 1.637      ;
; -0.686 ; toggle_counter[9]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.036     ; 1.637      ;
; -0.680 ; counter[5]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.630      ;
; -0.680 ; counter[5]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.630      ;
; -0.679 ; counter[1]         ; counter[19]        ; clk          ; clk         ; 1.000        ; 0.150      ; 1.816      ;
; -0.677 ; counter[3]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.627      ;
; -0.671 ; counter[5]         ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.037     ; 1.621      ;
; -0.671 ; counter[5]         ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.037     ; 1.621      ;
; -0.669 ; counter[0]         ; counter[19]        ; clk          ; clk         ; 1.000        ; 0.150      ; 1.806      ;
; -0.664 ; counter[10]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.036     ; 1.615      ;
; -0.662 ; counter[4]         ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.612      ;
; -0.662 ; counter[2]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.612      ;
; -0.661 ; counter[19]        ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.232     ; 1.416      ;
; -0.660 ; counter[18]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.036     ; 1.611      ;
; -0.660 ; counter[18]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.036     ; 1.611      ;
; -0.660 ; toggle_counter[11] ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.036     ; 1.611      ;
; -0.660 ; toggle_counter[11] ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.036     ; 1.611      ;
; -0.657 ; counter[19]        ; counter[10]        ; clk          ; clk         ; 1.000        ; -0.232     ; 1.412      ;
; -0.657 ; counter[19]        ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.232     ; 1.412      ;
; -0.657 ; counter[19]        ; counter[23]        ; clk          ; clk         ; 1.000        ; -0.232     ; 1.412      ;
; -0.656 ; counter[11]        ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.036     ; 1.607      ;
; -0.656 ; toggle_counter[0]  ; toggle_counter[24] ; clk          ; clk         ; 1.000        ; 0.153      ; 1.796      ;
; -0.652 ; toggle_counter[1]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.036     ; 1.603      ;
; -0.652 ; toggle_counter[4]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.036     ; 1.603      ;
; -0.652 ; toggle_counter[1]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.036     ; 1.603      ;
; -0.652 ; toggle_counter[4]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.036     ; 1.603      ;
; -0.651 ; counter[10]        ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.036     ; 1.602      ;
; -0.651 ; toggle_counter[0]  ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.036     ; 1.602      ;
; -0.651 ; toggle_counter[0]  ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.036     ; 1.602      ;
; -0.649 ; counter[17]        ; counter[19]        ; clk          ; clk         ; 1.000        ; 0.151      ; 1.787      ;
; -0.649 ; toggle_counter[0]  ; toggle_counter[22] ; clk          ; clk         ; 1.000        ; 0.153      ; 1.789      ;
; -0.647 ; counter[17]        ; led[0]~reg0        ; clk          ; clk         ; 1.000        ; 0.155      ; 1.789      ;
; -0.647 ; counter[17]        ; led[2]~reg0        ; clk          ; clk         ; 1.000        ; 0.155      ; 1.789      ;
; -0.647 ; counter[17]        ; led[3]~reg0        ; clk          ; clk         ; 1.000        ; 0.155      ; 1.789      ;
; -0.647 ; counter[17]        ; led[1]~reg0        ; clk          ; clk         ; 1.000        ; 0.155      ; 1.789      ;
; -0.647 ; counter[19]        ; counter[15]        ; clk          ; clk         ; 1.000        ; -0.233     ; 1.401      ;
; -0.647 ; counter[19]        ; counter[17]        ; clk          ; clk         ; 1.000        ; -0.233     ; 1.401      ;
; -0.646 ; counter[19]        ; counter[18]        ; clk          ; clk         ; 1.000        ; -0.233     ; 1.400      ;
; -0.642 ; toggle_counter[18] ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.236     ; 1.393      ;
; -0.641 ; toggle_counter[18] ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.236     ; 1.392      ;
; -0.639 ; toggle_counter[7]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.038     ; 1.588      ;
; -0.638 ; toggle_counter[7]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.038     ; 1.587      ;
; -0.637 ; counter[16]        ; counter[0]         ; clk          ; clk         ; 1.000        ; -0.036     ; 1.588      ;
; -0.637 ; counter[16]        ; counter[5]         ; clk          ; clk         ; 1.000        ; -0.036     ; 1.588      ;
; -0.635 ; toggle_counter[3]  ; toggle_counter[24] ; clk          ; clk         ; 1.000        ; 0.153      ; 1.775      ;
; -0.634 ; counter[19]        ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.232     ; 1.389      ;
; -0.633 ; toggle_counter[5]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.038     ; 1.582      ;
; -0.632 ; toggle_counter[5]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.038     ; 1.581      ;
; -0.630 ; counter[4]         ; counter[20]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.580      ;
; -0.629 ; toggle_counter[14] ; toggle_counter[0]  ; clk          ; clk         ; 1.000        ; -0.233     ; 1.383      ;
; -0.629 ; toggle_counter[14] ; toggle_counter[11] ; clk          ; clk         ; 1.000        ; -0.233     ; 1.383      ;
; -0.628 ; toggle_counter[3]  ; toggle_counter[22] ; clk          ; clk         ; 1.000        ; 0.153      ; 1.768      ;
; -0.624 ; counter[1]         ; counter[24]        ; clk          ; clk         ; 1.000        ; -0.038     ; 1.573      ;
; -0.623 ; counter[4]         ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.573      ;
; -0.623 ; toggle_counter[8]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.038     ; 1.572      ;
; -0.622 ; toggle_counter[8]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.038     ; 1.571      ;
; -0.621 ; counter[7]         ; counter[13]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.571      ;
; -0.621 ; toggle_counter[9]  ; toggle_counter[13] ; clk          ; clk         ; 1.000        ; -0.038     ; 1.570      ;
; -0.620 ; toggle_counter[9]  ; toggle_counter[6]  ; clk          ; clk         ; 1.000        ; -0.038     ; 1.569      ;
; -0.619 ; counter[4]         ; counter[10]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.569      ;
; -0.619 ; counter[4]         ; counter[21]        ; clk          ; clk         ; 1.000        ; -0.037     ; 1.569      ;
+--------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+


+-----------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'clk'                                                                                      ;
+-------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node          ; To Node            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+
; 0.179 ; toggle_pin~reg0    ; toggle_pin~reg0    ; clk          ; clk         ; 0.000        ; 0.044      ; 0.307      ;
; 0.185 ; led[0]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.314      ;
; 0.185 ; led[2]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.314      ;
; 0.185 ; led[3]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.314      ;
; 0.185 ; led[1]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.314      ;
; 0.204 ; counter[24]        ; counter[24]        ; clk          ; clk         ; 0.000        ; 0.037      ; 0.325      ;
; 0.205 ; led[0]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.334      ;
; 0.208 ; led[0]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.337      ;
; 0.210 ; led[1]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.339      ;
; 0.210 ; led[1]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.339      ;
; 0.296 ; counter[1]         ; counter[1]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.417      ;
; 0.297 ; toggle_counter[23] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.044      ; 0.425      ;
; 0.297 ; toggle_counter[9]  ; toggle_counter[9]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.417      ;
; 0.297 ; counter[7]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.418      ;
; 0.298 ; toggle_counter[10] ; toggle_counter[10] ; clk          ; clk         ; 0.000        ; 0.036      ; 0.418      ;
; 0.298 ; toggle_counter[2]  ; toggle_counter[2]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.418      ;
; 0.298 ; toggle_counter[1]  ; toggle_counter[1]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.418      ;
; 0.298 ; counter[3]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; counter[6]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; counter[2]         ; counter[2]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.419      ;
; 0.299 ; toggle_counter[8]  ; toggle_counter[8]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.419      ;
; 0.299 ; toggle_counter[3]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.419      ;
; 0.299 ; counter[4]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.420      ;
; 0.303 ; counter[9]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.424      ;
; 0.304 ; toggle_counter[7]  ; toggle_counter[7]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.424      ;
; 0.305 ; toggle_counter[15] ; toggle_counter[15] ; clk          ; clk         ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; toggle_counter[17] ; toggle_counter[17] ; clk          ; clk         ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; counter[14]        ; counter[14]        ; clk          ; clk         ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; led[1]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.434      ;
; 0.306 ; toggle_counter[5]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; toggle_counter[4]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; counter[22]        ; counter[22]        ; clk          ; clk         ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; counter[16]        ; counter[16]        ; clk          ; clk         ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; counter[8]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.427      ;
; 0.307 ; led[2]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.436      ;
; 0.347 ; led[0]~reg0        ; led[3]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.476      ;
; 0.348 ; led[3]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.477      ;
; 0.348 ; led[3]~reg0        ; led[2]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.477      ;
; 0.348 ; led[3]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.477      ;
; 0.349 ; led[2]~reg0        ; led[0]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.478      ;
; 0.349 ; led[2]~reg0        ; led[1]~reg0        ; clk          ; clk         ; 0.000        ; 0.045      ; 0.478      ;
; 0.408 ; toggle_counter[17] ; toggle_counter[18] ; clk          ; clk         ; 0.000        ; 0.235      ; 0.727      ;
; 0.445 ; counter[1]         ; counter[2]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.566      ;
; 0.446 ; toggle_counter[9]  ; toggle_counter[10] ; clk          ; clk         ; 0.000        ; 0.036      ; 0.566      ;
; 0.446 ; counter[7]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.567      ;
; 0.447 ; toggle_counter[1]  ; toggle_counter[2]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.567      ;
; 0.447 ; counter[3]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.568      ;
; 0.448 ; toggle_counter[3]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.568      ;
; 0.449 ; counter[5]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.570      ;
; 0.450 ; toggle_counter[21] ; toggle_counter[21] ; clk          ; clk         ; 0.000        ; 0.044      ; 0.578      ;
; 0.450 ; toggle_counter[17] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.235      ; 0.769      ;
; 0.453 ; toggle_counter[7]  ; toggle_counter[8]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.573      ;
; 0.454 ; toggle_counter[18] ; toggle_counter[18] ; clk          ; clk         ; 0.000        ; 0.044      ; 0.582      ;
; 0.455 ; counter[15]        ; counter[16]        ; clk          ; clk         ; 0.000        ; 0.037      ; 0.576      ;
; 0.456 ; counter[6]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.577      ;
; 0.456 ; counter[2]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.577      ;
; 0.456 ; toggle_counter[2]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.576      ;
; 0.457 ; counter[0]         ; counter[1]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.578      ;
; 0.457 ; toggle_counter[8]  ; toggle_counter[9]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.577      ;
; 0.458 ; toggle_counter[0]  ; toggle_counter[1]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.578      ;
; 0.459 ; counter[18]        ; counter[18]        ; clk          ; clk         ; 0.000        ; 0.037      ; 0.580      ;
; 0.459 ; counter[6]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.580      ;
; 0.459 ; counter[2]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.580      ;
; 0.459 ; toggle_counter[2]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.579      ;
; 0.460 ; counter[15]        ; counter[15]        ; clk          ; clk         ; 0.000        ; 0.037      ; 0.581      ;
; 0.460 ; counter[0]         ; counter[2]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.581      ;
; 0.460 ; toggle_counter[8]  ; toggle_counter[10] ; clk          ; clk         ; 0.000        ; 0.036      ; 0.580      ;
; 0.460 ; counter[4]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.581      ;
; 0.461 ; counter[17]        ; counter[17]        ; clk          ; clk         ; 0.000        ; 0.037      ; 0.582      ;
; 0.461 ; toggle_counter[0]  ; toggle_counter[2]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.581      ;
; 0.464 ; counter[8]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.585      ;
; 0.464 ; toggle_counter[4]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.584      ;
; 0.466 ; counter[14]        ; counter[16]        ; clk          ; clk         ; 0.000        ; 0.037      ; 0.587      ;
; 0.467 ; counter[22]        ; counter[24]        ; clk          ; clk         ; 0.000        ; 0.037      ; 0.588      ;
; 0.468 ; toggle_counter[15] ; toggle_counter[16] ; clk          ; clk         ; 0.000        ; 0.235      ; 0.787      ;
; 0.474 ; toggle_counter[15] ; toggle_counter[18] ; clk          ; clk         ; 0.000        ; 0.235      ; 0.793      ;
; 0.500 ; toggle_counter[24] ; toggle_counter[24] ; clk          ; clk         ; 0.000        ; 0.044      ; 0.628      ;
; 0.501 ; toggle_counter[24] ; toggle_counter[19] ; clk          ; clk         ; 0.000        ; 0.044      ; 0.629      ;
; 0.501 ; toggle_counter[24] ; toggle_counter[22] ; clk          ; clk         ; 0.000        ; 0.044      ; 0.629      ;
; 0.501 ; toggle_counter[24] ; toggle_pin~reg0    ; clk          ; clk         ; 0.000        ; 0.044      ; 0.629      ;
; 0.508 ; counter[1]         ; counter[3]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.629      ;
; 0.509 ; counter[7]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.630      ;
; 0.510 ; toggle_counter[21] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.044      ; 0.638      ;
; 0.510 ; toggle_counter[1]  ; toggle_counter[3]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.630      ;
; 0.511 ; counter[1]         ; counter[4]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.632      ;
; 0.511 ; toggle_counter[3]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.631      ;
; 0.512 ; counter[5]         ; counter[7]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.633      ;
; 0.513 ; toggle_counter[1]  ; toggle_counter[4]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.633      ;
; 0.513 ; counter[3]         ; counter[6]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.634      ;
; 0.513 ; toggle_counter[16] ; toggle_counter[16] ; clk          ; clk         ; 0.000        ; 0.044      ; 0.641      ;
; 0.515 ; counter[5]         ; counter[8]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.636      ;
; 0.516 ; toggle_counter[11] ; toggle_counter[11] ; clk          ; clk         ; 0.000        ; 0.036      ; 0.636      ;
; 0.516 ; toggle_counter[15] ; toggle_counter[23] ; clk          ; clk         ; 0.000        ; 0.235      ; 0.835      ;
; 0.516 ; toggle_counter[7]  ; toggle_counter[9]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.636      ;
; 0.517 ; toggle_counter[15] ; toggle_counter[17] ; clk          ; clk         ; 0.000        ; 0.036      ; 0.637      ;
; 0.518 ; toggle_counter[5]  ; toggle_counter[7]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.638      ;
; 0.519 ; toggle_counter[7]  ; toggle_counter[10] ; clk          ; clk         ; 0.000        ; 0.036      ; 0.639      ;
; 0.521 ; toggle_counter[5]  ; toggle_counter[8]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.641      ;
; 0.522 ; counter[6]         ; counter[9]         ; clk          ; clk         ; 0.000        ; 0.037      ; 0.643      ;
; 0.522 ; toggle_counter[2]  ; toggle_counter[5]  ; clk          ; clk         ; 0.000        ; 0.036      ; 0.642      ;
+-------+--------------------+--------------------+--------------+-------------+--------------+------------+------------+


----------------------------------------------
; Fast 1200mV 0C Model Metastability Summary ;
----------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                            ;
+------------------+----------+-------+----------+---------+---------------------+
; Clock            ; Setup    ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+------------------+----------+-------+----------+---------+---------------------+
; Worst-case Slack ; -3.136   ; 0.179 ; N/A      ; N/A     ; -3.000              ;
;  clk             ; -3.136   ; 0.179 ; N/A      ; N/A     ; -3.000              ;
; Design-wide TNS  ; -122.63  ; 0.0   ; 0.0      ; 0.0     ; -84.785             ;
;  clk             ; -122.630 ; 0.000 ; N/A      ; N/A     ; -84.785             ;
+------------------+----------+-------+----------+---------+---------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                    ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin           ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; led[0]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; led[1]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; led[2]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; led[3]        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; toggle_pin    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; clk                     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; led[0]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; led[1]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; led[2]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; led[3]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; toggle_pin    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.57e-09 V                   ; 2.37 V              ; -0.00683 V          ; 0.171 V                              ; 0.018 V                              ; 4.97e-10 s                  ; 6.66e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.57e-09 V                  ; 2.37 V             ; -0.00683 V         ; 0.171 V                             ; 0.018 V                             ; 4.97e-10 s                 ; 6.66e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; led[0]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; led[1]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; led[2]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; led[3]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; toggle_pin    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.54e-07 V                   ; 2.34 V              ; -0.00774 V          ; 0.109 V                              ; 0.026 V                              ; 6.58e-10 s                  ; 8.24e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.54e-07 V                  ; 2.34 V             ; -0.00774 V         ; 0.109 V                             ; 0.026 V                             ; 6.58e-10 s                 ; 8.24e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; led[0]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; led[1]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; led[2]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; led[3]        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; toggle_pin    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 3.54e-08 V                   ; 2.7 V               ; -0.00943 V          ; 0.276 V                              ; 0.035 V                              ; 3.19e-10 s                  ; 4.99e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 3.54e-08 V                  ; 2.7 V              ; -0.00943 V         ; 0.276 V                             ; 0.035 V                             ; 3.19e-10 s                 ; 4.99e-10 s                 ; No                        ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+-------------------------------------------------------------------+
; Setup Transfers                                                   ;
+------------+----------+----------+----------+----------+----------+
; From Clock ; To Clock ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+------------+----------+----------+----------+----------+----------+
; clk        ; clk      ; 1442     ; 0        ; 0        ; 0        ;
+------------+----------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+-------------------------------------------------------------------+
; Hold Transfers                                                    ;
+------------+----------+----------+----------+----------+----------+
; From Clock ; To Clock ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+------------+----------+----------+----------+----------+----------+
; clk        ; clk      ; 1442     ; 0        ; 0        ; 0        ;
+------------+----------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths Summary                    ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 0     ; 0    ;
; Unconstrained Input Ports       ; 0     ; 0    ;
; Unconstrained Input Port Paths  ; 0     ; 0    ;
; Unconstrained Output Ports      ; 5     ; 5    ;
; Unconstrained Output Port Paths ; 5     ; 5    ;
+---------------------------------+-------+------+


+-------------------------------------+
; Clock Status Summary                ;
+--------+-------+------+-------------+
; Target ; Clock ; Type ; Status      ;
+--------+-------+------+-------------+
; clk    ; clk   ; Base ; Constrained ;
+--------+-------+------+-------------+


+-----------------------------------------------------------------------------------------------------+
; Unconstrained Output Ports                                                                          ;
+-------------+---------------------------------------------------------------------------------------+
; Output Port ; Comment                                                                               ;
+-------------+---------------------------------------------------------------------------------------+
; led[0]      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; led[1]      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; led[2]      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; led[3]      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; toggle_pin  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
+-------------+---------------------------------------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------+
; Unconstrained Output Ports                                                                          ;
+-------------+---------------------------------------------------------------------------------------+
; Output Port ; Comment                                                                               ;
+-------------+---------------------------------------------------------------------------------------+
; led[0]      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; led[1]      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; led[2]      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; led[3]      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; toggle_pin  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
+-------------+---------------------------------------------------------------------------------------+


+--------------------------+
; Timing Analyzer Messages ;
+--------------------------+
Info: *******************************************************************
Info: Running Quartus Prime Timing Analyzer
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Thu Jul 31 14:08:27 2025
Info: Command: quartus_sta LED -c LED
Info: qsta_default_script.tcl version: #1
Warning (18236): Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance.
Info (20030): Parallel compilation is enabled and will use 14 of the 14 processors detected
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Critical Warning (332012): Synopsys Design Constraints File file not found: 'LED.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design.
Info (332142): No user constrained base clocks found in the design. Calling "derive_clocks -period 1.0"
Info (332105): Deriving Clocks
    Info (332105): create_clock -period 1.000 -name clk clk
Info (332143): No user constrained clock uncertainty found in the design. Calling "derive_clock_uncertainty"
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Info: Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -3.136
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.136            -122.630 clk 
Info (332146): Worst-case hold slack is 0.434
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.434               0.000 clk 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000             -84.785 clk 
Warning (18330): Ignoring Synchronizer Identification setting Off, and using Auto instead.
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -2.854
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -2.854            -108.785 clk 
Info (332146): Worst-case hold slack is 0.383
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.383               0.000 clk 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000             -84.785 clk 
Warning (18330): Ignoring Synchronizer Identification setting Off, and using Auto instead.
Info: Analyzing Fast 1200mV 0C Model
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -0.809
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -0.809             -23.249 clk 
Info (332146): Worst-case hold slack is 0.179
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.179               0.000 clk 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000             -61.861 clk 
Warning (18330): Ignoring Synchronizer Identification setting Off, and using Auto instead.
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus Prime Timing Analyzer was successful. 0 errors, 8 warnings
    Info: Peak virtual memory: 4868 megabytes
    Info: Processing ended: Thu Jul 31 14:08:28 2025
    Info: Elapsed time: 00:00:01
    Info: Total CPU time (on all processors): 00:00:01


