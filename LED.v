module LED(led, clk, toggle_pin);// 模块名及端口参数
output[3:0]led;				// 输出端口定义
output toggle_pin;          // 1秒翻转引脚输出
input clk; // 输入端口定义，25M 时钟 (MCC-C开发板)
reg[3:0] led;//变量led_out 定义为寄存器型
reg toggle_pin;             // 1秒翻转引脚寄存器
reg[24:0] counter;//变量led_out 定义为寄存器型
reg[24:0] toggle_counter;   // 1秒翻转计数器

always@(posedge clk)
begin
    counter<=counter+1;
    toggle_counter<=toggle_counter+1;

    // LED流水灯逻辑 (0.5秒)
	if(counter==25'd12500000)  // 25MHz时钟，0.5秒计数
	begin
		led<=led<<1;// led 向左移位，空闲位自动添0 补位
		counter<=0;//计数器清0
		if(led==8'b0000)//每到时间临界点后,左移一位,一直到8位全部都变为0
		led<=8'b1111;//重新赋值为全1,
	end

	// 1秒翻转引脚逻辑
	if(toggle_counter==25'd25000000)  // 25MHz时钟，1秒计数
	begin
		toggle_pin<=~toggle_pin;  // 翻转引脚状态
		toggle_counter<=0;        // 计数器清0
	end

end
endmodule
	