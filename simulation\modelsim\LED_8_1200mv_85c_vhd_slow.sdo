// Copyright (C) 2018  Intel Corporation. All rights reserved.
// Your use of Intel Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Intel Program License 
// Subscription Agreement, the Intel Quartus Prime License Agreement,
// the Intel FPGA IP License Agreement, or other applicable license
// agreement, including, without limitation, that your use is for
// the sole purpose of programming logic devices manufactured by
// Intel and sold by Intel or its authorized distributors.  Please
// refer to the applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8,
// with speed grade 8, core voltage 1.2VmV, and temperature 85 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (VHDL) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "LED")
  (DATE "07/31/2025 14:08:29")
  (VENDOR "Altera")
  (PROGRAM "Quartus Prime")
  (VERSION "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[0\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1068:1068:1068) (996:996:996))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[1\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1152:1152:1152) (1090:1090:1090))
        (IOPATH i o (3068:3068:3068) (3029:3029:3029))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[2\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1157:1157:1157) (1097:1097:1097))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[3\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1174:1174:1174) (1113:1113:1113))
        (IOPATH i o (3158:3158:3158) (3135:3135:3135))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\toggle_pin\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1855:1855:1855) (1738:1738:1738))
        (IOPATH i o (3157:3157:3157) (3105:3105:3105))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE \\clk\~input\\)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (766:766:766) (812:812:812))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE \\clk\~inputclkctrl\\)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (222:222:222) (208:208:208))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (382:382:382) (480:480:480))
        (PORT datab (381:381:381) (464:464:464))
        (PORT datad (340:340:340) (420:420:420))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (340:340:340) (422:422:422))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (237:237:237) (264:264:264))
        (PORT datad (863:863:863) (802:802:802))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (344:344:344) (430:430:430))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (344:344:344) (433:433:433))
        (IOPATH dataa combout (471:471:471) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (341:341:341) (422:422:422))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (341:341:341) (423:423:423))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (342:342:342) (422:422:422))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (279:279:279) (304:304:304))
        (PORT datad (863:863:863) (802:802:802))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (341:341:341) (419:419:419))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (432:432:432))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (358:358:358) (435:435:435))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (361:361:361) (444:444:444))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1614:1614:1614) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (914:914:914) (880:880:880))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (357:357:357) (418:418:418))
        (PORT datad (819:819:819) (764:764:764))
        (IOPATH dataa combout (421:421:421) (428:428:428))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (938:938:938) (929:929:929))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (757:757:757) (673:673:673))
        (PORT datad (317:317:317) (370:370:370))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (626:626:626) (635:635:635))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (476:476:476) (445:445:445))
        (PORT datad (316:316:316) (369:369:369))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (573:573:573) (604:604:604))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (361:361:361) (422:422:422))
        (PORT datad (469:469:469) (437:437:437))
        (IOPATH dataa combout (421:421:421) (428:428:428))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (358:358:358) (434:434:434))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1650:1650:1650))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (364:364:364) (448:448:448))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (522:522:522) (524:524:524))
        (PORT datad (240:240:240) (259:259:259))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1650:1650:1650))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (359:359:359) (435:435:435))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1650:1650:1650))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (360:360:360) (437:437:437))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (522:522:522) (523:523:523))
        (PORT datad (239:239:239) (258:258:258))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1650:1650:1650))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (363:363:363) (446:446:446))
        (IOPATH dataa combout (471:471:471) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (522:522:522) (523:523:523))
        (PORT datad (239:239:239) (257:257:257))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1650:1650:1650))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (914:914:914) (901:901:901))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (840:840:840) (796:796:796))
        (PORT datad (847:847:847) (783:783:783))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2124:2124:2124))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (923:923:923) (919:919:919))
        (PORT datab (361:361:361) (438:438:438))
        (PORT datac (858:858:858) (850:850:850))
        (PORT datad (896:896:896) (893:893:893))
        (IOPATH dataa combout (392:392:392) (398:398:398))
        (IOPATH datab combout (393:393:393) (408:408:408))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (625:625:625) (633:633:633))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (448:448:448) (424:424:424))
        (PORT datad (315:315:315) (368:368:368))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (640:640:640) (646:646:646))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (357:357:357) (418:418:418))
        (PORT datad (437:437:437) (414:414:414))
        (IOPATH dataa combout (421:421:421) (428:428:428))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (360:360:360) (436:436:436))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1650:1650:1650))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (581:581:581) (615:615:615))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (357:357:357) (418:418:418))
        (PORT datad (438:438:438) (415:415:415))
        (IOPATH dataa combout (421:421:421) (428:428:428))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1651:1651:1651))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (322:322:322) (392:392:392))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1613:1613:1613) (1650:1650:1650))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (573:573:573) (605:605:605))
        (PORT datab (616:616:616) (620:620:620))
        (PORT datac (319:319:319) (396:396:396))
        (PORT datad (319:319:319) (389:389:389))
        (IOPATH dataa combout (405:405:405) (398:398:398))
        (IOPATH datab combout (432:432:432) (433:433:433))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (345:345:345) (434:434:434))
        (PORT datab (342:342:342) (424:424:424))
        (PORT datac (299:299:299) (383:383:383))
        (PORT datad (302:302:302) (379:379:379))
        (IOPATH dataa combout (438:438:438) (448:448:448))
        (IOPATH datab combout (440:440:440) (462:462:462))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (361:361:361) (444:444:444))
        (PORT datab (362:362:362) (439:439:439))
        (PORT datac (885:885:885) (872:872:872))
        (PORT datad (868:868:868) (861:861:861))
        (IOPATH dataa combout (448:448:448) (472:472:472))
        (IOPATH datab combout (454:454:454) (473:473:473))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (446:446:446))
        (PORT datab (559:559:559) (584:584:584))
        (PORT datac (517:517:517) (549:549:549))
        (PORT datad (319:319:319) (389:389:389))
        (IOPATH dataa combout (392:392:392) (398:398:398))
        (IOPATH datab combout (393:393:393) (408:408:408))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (344:344:344) (433:433:433))
        (PORT datab (339:339:339) (421:421:421))
        (PORT datac (301:301:301) (385:385:385))
        (PORT datad (302:302:302) (378:378:378))
        (IOPATH dataa combout (456:456:456) (486:486:486))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (875:875:875) (814:814:814))
        (PORT datab (277:277:277) (302:302:302))
        (PORT datac (236:236:236) (262:262:262))
        (PORT datad (818:818:818) (758:758:758))
        (IOPATH dataa combout (392:392:392) (398:398:398))
        (IOPATH datab combout (393:393:393) (408:408:408))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (813:813:813) (771:771:771))
        (PORT datab (617:617:617) (621:621:621))
        (PORT datac (237:237:237) (263:263:263))
        (PORT datad (238:238:238) (257:257:257))
        (IOPATH dataa combout (405:405:405) (398:398:398))
        (IOPATH datab combout (432:432:432) (433:433:433))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[3\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2107:2107:2107) (2128:2128:2128))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (1309:1309:1309) (1245:1245:1245))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (382:382:382) (479:479:479))
        (PORT datab (379:379:379) (469:469:469))
        (PORT datad (340:340:340) (421:421:421))
        (IOPATH dataa combout (456:456:456) (486:486:486))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[1\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2107:2107:2107) (2128:2128:2128))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (1309:1309:1309) (1245:1245:1245))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (382:382:382) (473:473:473))
        (PORT datab (379:379:379) (470:470:470))
        (PORT datad (339:339:339) (421:421:421))
        (IOPATH dataa combout (453:453:453) (472:472:472))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[2\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2107:2107:2107) (2128:2128:2128))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (1309:1309:1309) (1245:1245:1245))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (383:383:383) (480:480:480))
        (PORT datab (379:379:379) (470:470:470))
        (PORT datad (340:340:340) (421:421:421))
        (IOPATH dataa combout (456:456:456) (486:486:486))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[0\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2107:2107:2107) (2128:2128:2128))
        (PORT d (99:99:99) (115:115:115))
        (PORT ena (1309:1309:1309) (1245:1245:1245))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
      (HOLD ena (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (340:340:340) (422:422:422))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (237:237:237) (264:264:264))
        (PORT datad (862:862:862) (813:813:813))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (344:344:344) (433:433:433))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (344:344:344) (430:430:430))
        (IOPATH dataa combout (471:471:471) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (341:341:341) (422:422:422))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (359:359:359) (435:435:435))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (359:359:359) (435:435:435))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (940:940:940) (943:943:943))
        (IOPATH dataa combout (471:471:471) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (374:374:374) (438:438:438))
        (PORT datad (834:834:834) (775:775:775))
        (IOPATH datab combout (435:435:435) (433:433:433))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1610:1610:1610) (1643:1643:1643))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (361:361:361) (445:445:445))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (339:339:339) (421:421:421))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (430:430:430))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (339:339:339) (417:417:417))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (344:344:344) (425:425:425))
        (IOPATH datab combout (473:473:473) (487:487:487))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (239:239:239) (266:266:266))
        (PORT datad (862:862:862) (813:813:813))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1646:1646:1646))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (566:566:566) (585:585:585))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (494:494:494) (468:468:468))
        (PORT datad (331:331:331) (391:391:391))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1610:1610:1610) (1643:1643:1643))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (557:557:557) (593:593:593))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (375:375:375) (439:439:439))
        (PORT datad (451:451:451) (429:429:429))
        (IOPATH datab combout (435:435:435) (433:433:433))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1610:1610:1610) (1643:1643:1643))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (559:559:559) (584:584:584))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (371:371:371) (435:435:435))
        (PORT datad (487:487:487) (460:460:460))
        (IOPATH datab combout (435:435:435) (433:433:433))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2128:2128:2128))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (361:361:361) (445:445:445))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1644:1644:1644))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (363:363:363) (447:447:447))
        (IOPATH dataa combout (471:471:471) (472:472:472))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (238:238:238) (265:265:265))
        (PORT datad (525:525:525) (509:509:509))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2118:2118:2118))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (445:445:445))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1609:1609:1609) (1644:1644:1644))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (360:360:360) (437:437:437))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (585:585:585) (558:558:558))
        (PORT datad (239:239:239) (258:258:258))
        (IOPATH datab combout (435:435:435) (433:433:433))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2118:2118:2118))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (576:576:576) (599:599:599))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (492:492:492) (466:466:466))
        (PORT datad (330:330:330) (389:389:389))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2128:2128:2128))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (841:841:841) (828:828:828))
        (PORT datab (361:361:361) (437:437:437))
        (PORT datac (533:533:533) (561:561:561))
        (PORT datad (864:864:864) (822:822:822))
        (IOPATH dataa combout (392:392:392) (398:398:398))
        (IOPATH datab combout (393:393:393) (408:408:408))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (559:559:559) (585:585:585))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (372:372:372) (436:436:436))
        (PORT datad (446:446:446) (419:419:419))
        (IOPATH datab combout (435:435:435) (433:433:433))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2128:2128:2128))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (445:445:445))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (585:585:585) (558:558:558))
        (PORT datad (238:238:238) (256:256:256))
        (IOPATH datab combout (435:435:435) (433:433:433))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2118:2118:2118))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (613:613:613) (617:617:617))
        (IOPATH datab combout (472:472:472) (473:473:473))
        (IOPATH datab cout (565:565:565) (421:421:421))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (445:445:445) (426:426:426))
        (PORT datad (328:328:328) (387:387:387))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2128:2128:2128))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (446:446:446))
        (IOPATH dataa combout (461:461:461) (481:481:481))
        (IOPATH dataa cout (552:552:552) (416:416:416))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
        (IOPATH cin cout (73:73:73) (73:73:73))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2118:2118:2118))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (556:556:556) (570:570:570))
        (IOPATH datad combout (177:177:177) (155:155:155))
        (IOPATH cin combout (607:607:607) (577:577:577))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (372:372:372) (436:436:436))
        (PORT datad (480:480:480) (448:448:448))
        (IOPATH datab combout (435:435:435) (433:433:433))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2128:2128:2128))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (446:446:446))
        (PORT datab (359:359:359) (435:435:435))
        (PORT datac (796:796:796) (776:776:776))
        (PORT datad (565:565:565) (583:583:583))
        (IOPATH dataa combout (432:432:432) (446:446:446))
        (IOPATH datab combout (437:437:437) (436:436:436))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (962:962:962) (970:970:970))
        (PORT datab (360:360:360) (436:436:436))
        (PORT datac (1152:1152:1152) (1095:1095:1095))
        (PORT datad (1200:1200:1200) (1140:1140:1140))
        (IOPATH dataa combout (481:481:481) (491:491:491))
        (IOPATH datab combout (472:472:472) (452:452:452))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (445:445:445))
        (PORT datab (359:359:359) (436:436:436))
        (PORT datac (321:321:321) (400:400:400))
        (PORT datad (813:813:813) (788:788:788))
        (IOPATH dataa combout (432:432:432) (446:446:446))
        (IOPATH datab combout (437:437:437) (436:436:436))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (344:344:344) (433:433:433))
        (PORT datab (339:339:339) (421:421:421))
        (PORT datac (301:301:301) (385:385:385))
        (PORT datad (302:302:302) (378:378:378))
        (IOPATH dataa combout (456:456:456) (486:486:486))
        (IOPATH datab combout (457:457:457) (489:489:489))
        (IOPATH datac combout (324:324:324) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (345:345:345) (434:434:434))
        (PORT datab (342:342:342) (424:424:424))
        (PORT datac (299:299:299) (383:383:383))
        (PORT datad (303:303:303) (379:379:379))
        (IOPATH dataa combout (438:438:438) (448:448:448))
        (IOPATH datab combout (440:440:440) (462:462:462))
        (IOPATH datac combout (327:327:327) (315:315:315))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (280:280:280) (312:312:312))
        (PORT datab (278:278:278) (303:303:303))
        (PORT datac (830:830:830) (780:780:780))
        (PORT datad (1140:1140:1140) (1010:1010:1010))
        (IOPATH dataa combout (392:392:392) (398:398:398))
        (IOPATH datab combout (393:393:393) (408:408:408))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (281:281:281) (313:313:313))
        (PORT datab (361:361:361) (437:437:437))
        (PORT datac (236:236:236) (263:263:263))
        (PORT datad (238:238:238) (256:256:256))
        (IOPATH dataa combout (392:392:392) (398:398:398))
        (IOPATH datab combout (393:393:393) (408:408:408))
        (IOPATH datac combout (324:324:324) (316:316:316))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_pin\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (332:332:332) (392:392:392))
        (IOPATH datac combout (462:462:462) (482:482:482))
        (IOPATH datad combout (177:177:177) (155:155:155))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_pin\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (2104:2104:2104) (2128:2128:2128))
        (PORT d (99:99:99) (115:115:115))
        (IOPATH (posedge clk) q (261:261:261) (261:261:261))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (212:212:212))
    )
  )
)
