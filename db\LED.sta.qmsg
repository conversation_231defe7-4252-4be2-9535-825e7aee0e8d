{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1753942107803 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Timing Analyzer Quartus Prime " "Running Quartus Prime Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1753942107807 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 14:08:27 2025 " "Processing started: Thu Jul 31 14:08:27 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1753942107807 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Timing Analyzer" 0 -1 1753942107807 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta LED -c LED " "Command: quartus_sta LED -c LED" {  } {  } 0 0 "Command: %1!s!" 0 0 "Timing Analyzer" 0 -1 1753942107807 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Timing Analyzer" 0 0 1753942107847 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Timing Analyzer" 0 -1 1753942107911 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Timing Analyzer" 0 -1 1753942107911 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942107943 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942107943 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "LED.sdc " "Synopsys Design Constraints File file not found: 'LED.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Timing Analyzer" 0 -1 1753942108028 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "base clocks \"derive_clocks -period 1.0\" " "No user constrained base clocks found in the design. Calling \"derive_clocks -period 1.0\"" {  } {  } 0 332142 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942108028 ""}
{ "Info" "ISTA_DERIVE_CLOCKS_INFO" "Deriving Clocks " "Deriving Clocks" { { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name clk clk " "create_clock -period 1.000 -name clk clk" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1753942108029 ""}  } {  } 0 332105 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1753942108029 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Timing Analyzer" 0 -1 1753942108029 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1753942108029 ""}
{ "Info" "0" "" "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Timing Analyzer" 0 0 1753942108030 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Timing Analyzer" 0 0 1753942108035 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1753942108042 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1753942108042 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -3.136 " "Worst-case setup slack is -3.136" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108043 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108043 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.136            -122.630 clk  " "   -3.136            -122.630 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108043 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942108043 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.434 " "Worst-case hold slack is 0.434" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108044 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108044 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.434               0.000 clk  " "    0.434               0.000 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108044 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942108044 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753942108045 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753942108046 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108048 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108048 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -84.785 clk  " "   -3.000             -84.785 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108048 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942108048 ""}
{ "Warning" "WSTA_METASTABILITY_REPORT_DISALLOW_GLOBAL_OFF" "" "Ignoring Synchronizer Identification setting Off, and using Auto instead." {  } {  } 0 18330 "Ignoring Synchronizer Identification setting Off, and using Auto instead." 0 0 "Timing Analyzer" 0 -1 1753942108055 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1753942108057 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Timing Analyzer" 0 -1 1753942108072 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Timing Analyzer" 0 -1 1753942108210 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1753942108234 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1753942108237 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1753942108237 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -2.854 " "Worst-case setup slack is -2.854" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108238 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108238 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.854            -108.785 clk  " "   -2.854            -108.785 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108238 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942108238 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.383 " "Worst-case hold slack is 0.383" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108240 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108240 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.383               0.000 clk  " "    0.383               0.000 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108240 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942108240 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753942108242 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753942108243 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108245 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108245 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -84.785 clk  " "   -3.000             -84.785 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108245 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942108245 ""}
{ "Warning" "WSTA_METASTABILITY_REPORT_DISALLOW_GLOBAL_OFF" "" "Ignoring Synchronizer Identification setting Off, and using Auto instead." {  } {  } 0 18330 "Ignoring Synchronizer Identification setting Off, and using Auto instead." 0 0 "Timing Analyzer" 0 -1 1753942108251 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1753942108254 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1753942108310 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1753942108311 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1753942108311 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -0.809 " "Worst-case setup slack is -0.809" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108313 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108313 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.809             -23.249 clk  " "   -0.809             -23.249 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108313 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942108313 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.179 " "Worst-case hold slack is 0.179" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108315 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108315 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.179               0.000 clk  " "    0.179               0.000 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108315 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942108315 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753942108317 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753942108319 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108320 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108320 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -61.861 clk  " "   -3.000             -61.861 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753942108320 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753942108320 ""}
{ "Warning" "WSTA_METASTABILITY_REPORT_DISALLOW_GLOBAL_OFF" "" "Ignoring Synchronizer Identification setting Off, and using Auto instead." {  } {  } 0 18330 "Ignoring Synchronizer Identification setting Off, and using Auto instead." 0 0 "Timing Analyzer" 0 -1 1753942108327 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1753942108516 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1753942108516 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Timing Analyzer 0 s 8 s Quartus Prime " "Quartus Prime Timing Analyzer was successful. 0 errors, 8 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4868 " "Peak virtual memory: 4868 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1753942108539 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 14:08:28 2025 " "Processing ended: Thu Jul 31 14:08:28 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1753942108539 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1753942108539 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1753942108539 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Timing Analyzer" 0 -1 1753942108539 ""}
