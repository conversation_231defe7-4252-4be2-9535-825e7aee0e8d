vendor_name = ModelSim
source_file = 1, C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v
source_file = 1, C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/db/LED.cbx.xml
design_name = hard_block
design_name = LED
instance = comp, \led[0]~output\, led[0]~output, LED, 1
instance = comp, \led[1]~output\, led[1]~output, LED, 1
instance = comp, \led[2]~output\, led[2]~output, LED, 1
instance = comp, \led[3]~output\, led[3]~output, LED, 1
instance = comp, \toggle_pin~output\, toggle_pin~output, LED, 1
instance = comp, \clk~input\, clk~input, LED, 1
instance = comp, \clk~inputclkctrl\, clk~inputclkctrl, LED, 1
instance = comp, \led~2\, led~2, LED, 1
instance = comp, \Add0~0\, Add0~0, LED, 1
instance = comp, \counter~0\, counter~0, LED, 1
instance = comp, \counter[0]\, counter[0], LED, 1
instance = comp, \Add0~2\, Add0~2, LED, 1
instance = comp, \counter[1]\, counter[1], LED, 1
instance = comp, \Add0~4\, Add0~4, LED, 1
instance = comp, \counter[2]\, counter[2], LED, 1
instance = comp, \Add0~6\, Add0~6, LED, 1
instance = comp, \counter[3]\, counter[3], LED, 1
instance = comp, \Add0~8\, Add0~8, LED, 1
instance = comp, \counter[4]\, counter[4], LED, 1
instance = comp, \Add0~10\, Add0~10, LED, 1
instance = comp, \counter~1\, counter~1, LED, 1
instance = comp, \counter[5]\, counter[5], LED, 1
instance = comp, \Add0~12\, Add0~12, LED, 1
instance = comp, \counter[6]\, counter[6], LED, 1
instance = comp, \Add0~14\, Add0~14, LED, 1
instance = comp, \counter[7]\, counter[7], LED, 1
instance = comp, \Add0~16\, Add0~16, LED, 1
instance = comp, \counter[8]\, counter[8], LED, 1
instance = comp, \Add0~18\, Add0~18, LED, 1
instance = comp, \counter[9]\, counter[9], LED, 1
instance = comp, \Add0~20\, Add0~20, LED, 1
instance = comp, \counter~2\, counter~2, LED, 1
instance = comp, \counter[10]\, counter[10], LED, 1
instance = comp, \Add0~22\, Add0~22, LED, 1
instance = comp, \counter~3\, counter~3, LED, 1
instance = comp, \counter[11]\, counter[11], LED, 1
instance = comp, \Add0~24\, Add0~24, LED, 1
instance = comp, \counter~4\, counter~4, LED, 1
instance = comp, \counter[12]\, counter[12], LED, 1
instance = comp, \Add0~26\, Add0~26, LED, 1
instance = comp, \counter~5\, counter~5, LED, 1
instance = comp, \counter[13]\, counter[13], LED, 1
instance = comp, \Add0~28\, Add0~28, LED, 1
instance = comp, \counter[14]\, counter[14], LED, 1
instance = comp, \Add0~30\, Add0~30, LED, 1
instance = comp, \counter~6\, counter~6, LED, 1
instance = comp, \counter[15]\, counter[15], LED, 1
instance = comp, \Add0~32\, Add0~32, LED, 1
instance = comp, \counter[16]\, counter[16], LED, 1
instance = comp, \Add0~34\, Add0~34, LED, 1
instance = comp, \counter~7\, counter~7, LED, 1
instance = comp, \counter[17]\, counter[17], LED, 1
instance = comp, \Add0~36\, Add0~36, LED, 1
instance = comp, \counter~8\, counter~8, LED, 1
instance = comp, \counter[18]\, counter[18], LED, 1
instance = comp, \Add0~38\, Add0~38, LED, 1
instance = comp, \counter~9\, counter~9, LED, 1
instance = comp, \counter[19]\, counter[19], LED, 1
instance = comp, \Equal0~5\, Equal0~5, LED, 1
instance = comp, \Add0~40\, Add0~40, LED, 1
instance = comp, \counter~10\, counter~10, LED, 1
instance = comp, \counter[20]\, counter[20], LED, 1
instance = comp, \Add0~42\, Add0~42, LED, 1
instance = comp, \counter~11\, counter~11, LED, 1
instance = comp, \counter[21]\, counter[21], LED, 1
instance = comp, \Add0~44\, Add0~44, LED, 1
instance = comp, \counter[22]\, counter[22], LED, 1
instance = comp, \Add0~46\, Add0~46, LED, 1
instance = comp, \counter~12\, counter~12, LED, 1
instance = comp, \counter[23]\, counter[23], LED, 1
instance = comp, \Add0~48\, Add0~48, LED, 1
instance = comp, \counter[24]\, counter[24], LED, 1
instance = comp, \Equal0~6\, Equal0~6, LED, 1
instance = comp, \Equal0~1\, Equal0~1, LED, 1
instance = comp, \Equal0~2\, Equal0~2, LED, 1
instance = comp, \Equal0~3\, Equal0~3, LED, 1
instance = comp, \Equal0~0\, Equal0~0, LED, 1
instance = comp, \Equal0~4\, Equal0~4, LED, 1
instance = comp, \Equal0~7\, Equal0~7, LED, 1
instance = comp, \led[3]~reg0\, led[3]~reg0, LED, 1
instance = comp, \led~0\, led~0, LED, 1
instance = comp, \led[1]~reg0\, led[1]~reg0, LED, 1
instance = comp, \led~1\, led~1, LED, 1
instance = comp, \led[2]~reg0\, led[2]~reg0, LED, 1
instance = comp, \Equal1~0\, Equal1~0, LED, 1
instance = comp, \led[0]~reg0\, led[0]~reg0, LED, 1
instance = comp, \Add1~0\, Add1~0, LED, 1
instance = comp, \toggle_counter~0\, toggle_counter~0, LED, 1
instance = comp, \toggle_counter[0]\, toggle_counter[0], LED, 1
instance = comp, \Add1~2\, Add1~2, LED, 1
instance = comp, \toggle_counter[1]\, toggle_counter[1], LED, 1
instance = comp, \Add1~4\, Add1~4, LED, 1
instance = comp, \toggle_counter[2]\, toggle_counter[2], LED, 1
instance = comp, \Add1~6\, Add1~6, LED, 1
instance = comp, \toggle_counter[3]\, toggle_counter[3], LED, 1
instance = comp, \Add1~8\, Add1~8, LED, 1
instance = comp, \toggle_counter[4]\, toggle_counter[4], LED, 1
instance = comp, \Add1~10\, Add1~10, LED, 1
instance = comp, \toggle_counter[5]\, toggle_counter[5], LED, 1
instance = comp, \Add1~12\, Add1~12, LED, 1
instance = comp, \toggle_counter~1\, toggle_counter~1, LED, 1
instance = comp, \toggle_counter[6]\, toggle_counter[6], LED, 1
instance = comp, \Add1~14\, Add1~14, LED, 1
instance = comp, \toggle_counter[7]\, toggle_counter[7], LED, 1
instance = comp, \Add1~16\, Add1~16, LED, 1
instance = comp, \toggle_counter[8]\, toggle_counter[8], LED, 1
instance = comp, \Add1~18\, Add1~18, LED, 1
instance = comp, \toggle_counter[9]\, toggle_counter[9], LED, 1
instance = comp, \Add1~20\, Add1~20, LED, 1
instance = comp, \toggle_counter[10]\, toggle_counter[10], LED, 1
instance = comp, \Add1~22\, Add1~22, LED, 1
instance = comp, \toggle_counter~2\, toggle_counter~2, LED, 1
instance = comp, \toggle_counter[11]\, toggle_counter[11], LED, 1
instance = comp, \Add1~24\, Add1~24, LED, 1
instance = comp, \toggle_counter~3\, toggle_counter~3, LED, 1
instance = comp, \toggle_counter[12]\, toggle_counter[12], LED, 1
instance = comp, \Add1~26\, Add1~26, LED, 1
instance = comp, \toggle_counter~4\, toggle_counter~4, LED, 1
instance = comp, \toggle_counter[13]\, toggle_counter[13], LED, 1
instance = comp, \Add1~28\, Add1~28, LED, 1
instance = comp, \toggle_counter~5\, toggle_counter~5, LED, 1
instance = comp, \toggle_counter[14]\, toggle_counter[14], LED, 1
instance = comp, \Add1~30\, Add1~30, LED, 1
instance = comp, \toggle_counter[15]\, toggle_counter[15], LED, 1
instance = comp, \Add1~32\, Add1~32, LED, 1
instance = comp, \toggle_counter~6\, toggle_counter~6, LED, 1
instance = comp, \toggle_counter[16]\, toggle_counter[16], LED, 1
instance = comp, \Add1~34\, Add1~34, LED, 1
instance = comp, \toggle_counter[17]\, toggle_counter[17], LED, 1
instance = comp, \Add1~36\, Add1~36, LED, 1
instance = comp, \toggle_counter~7\, toggle_counter~7, LED, 1
instance = comp, \toggle_counter[18]\, toggle_counter[18], LED, 1
instance = comp, \Add1~38\, Add1~38, LED, 1
instance = comp, \toggle_counter~8\, toggle_counter~8, LED, 1
instance = comp, \toggle_counter[19]\, toggle_counter[19], LED, 1
instance = comp, \Equal2~5\, Equal2~5, LED, 1
instance = comp, \Add1~40\, Add1~40, LED, 1
instance = comp, \toggle_counter~10\, toggle_counter~10, LED, 1
instance = comp, \toggle_counter[20]\, toggle_counter[20], LED, 1
instance = comp, \Add1~42\, Add1~42, LED, 1
instance = comp, \toggle_counter~11\, toggle_counter~11, LED, 1
instance = comp, \toggle_counter[21]\, toggle_counter[21], LED, 1
instance = comp, \Add1~44\, Add1~44, LED, 1
instance = comp, \toggle_counter~12\, toggle_counter~12, LED, 1
instance = comp, \toggle_counter[22]\, toggle_counter[22], LED, 1
instance = comp, \Add1~46\, Add1~46, LED, 1
instance = comp, \toggle_counter[23]\, toggle_counter[23], LED, 1
instance = comp, \Add1~48\, Add1~48, LED, 1
instance = comp, \toggle_counter~9\, toggle_counter~9, LED, 1
instance = comp, \toggle_counter[24]\, toggle_counter[24], LED, 1
instance = comp, \Equal2~6\, Equal2~6, LED, 1
instance = comp, \Equal2~1\, Equal2~1, LED, 1
instance = comp, \Equal2~3\, Equal2~3, LED, 1
instance = comp, \Equal2~0\, Equal2~0, LED, 1
instance = comp, \Equal2~2\, Equal2~2, LED, 1
instance = comp, \Equal2~4\, Equal2~4, LED, 1
instance = comp, \Equal2~7\, Equal2~7, LED, 1
instance = comp, \toggle_pin~0\, toggle_pin~0, LED, 1
instance = comp, \toggle_pin~reg0\, toggle_pin~reg0, LED, 1
