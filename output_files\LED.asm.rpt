Assembler report for LED
Thu Jul 31 14:08:26 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Assembler Summary
  3. Assembler Settings
  4. Assembler Generated Files
  5. Assembler Device Options: C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/output_files/LED.sof
  6. Assembler Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+---------------------------------------------------------------+
; Assembler Summary                                             ;
+-----------------------+---------------------------------------+
; Assembler Status      ; Successful - Thu Jul 31 14:08:26 2025 ;
; Revision Name         ; LED                                   ;
; Top-level Entity Name ; LED                                   ;
; Family                ; Cyclone IV E                          ;
; Device                ; EP4CE6E22C8                           ;
+-----------------------+---------------------------------------+


+----------------------------------+
; Assembler Settings               ;
+--------+---------+---------------+
; Option ; Setting ; Default Value ;
+--------+---------+---------------+


+---------------------------------------------------------------------------+
; Assembler Generated Files                                                 ;
+---------------------------------------------------------------------------+
; File Name                                                                 ;
+---------------------------------------------------------------------------+
; C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/output_files/LED.sof ;
+---------------------------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------+
; Assembler Device Options: C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/output_files/LED.sof ;
+----------------+------------------------------------------------------------------------------------+
; Option         ; Setting                                                                            ;
+----------------+------------------------------------------------------------------------------------+
; JTAG usercode  ; 0x0009C9D6                                                                         ;
; Checksum       ; 0x0009C9D6                                                                         ;
+----------------+------------------------------------------------------------------------------------+


+--------------------+
; Assembler Messages ;
+--------------------+
Info: *******************************************************************
Info: Running Quartus Prime Assembler
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Thu Jul 31 14:08:26 2025
Info: Command: quartus_asm --read_settings_files=off --write_settings_files=off LED -c LED
Warning (18236): Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance.
Info (115031): Writing out detailed assembly data for power analysis
Info (115030): Assembler is generating device programming files
Info: Quartus Prime Assembler was successful. 0 errors, 1 warning
    Info: Peak virtual memory: 4673 megabytes
    Info: Processing ended: Thu Jul 31 14:08:26 2025
    Info: Elapsed time: 00:00:00
    Info: Total CPU time (on all processors): 00:00:00


