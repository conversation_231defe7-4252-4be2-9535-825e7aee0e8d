// Copyright (C) 2018  Intel Corporation. All rights reserved.
// Your use of Intel Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Intel Program License 
// Subscription Agreement, the Intel Quartus Prime License Agreement,
// the Intel FPGA IP License Agreement, or other applicable license
// agreement, including, without limitation, that your use is for
// the sole purpose of programming logic devices manufactured by
// Intel and sold by Intel or its authorized distributors.  Please
// refer to the applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Slow Corner delays for the design using part EP4CE6E22C8,
// with speed grade 8, core voltage 1.2VmV, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (VHDL) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "LED")
  (DATE "07/31/2025 14:08:29")
  (VENDOR "Altera")
  (PROGRAM "Quartus Prime")
  (VERSION "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[0\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1027:1027:1027) (891:891:891))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[1\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1106:1106:1106) (977:977:977))
        (IOPATH i o (2717:2717:2717) (2696:2696:2696))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[2\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1110:1110:1110) (983:983:983))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[3\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1124:1124:1124) (997:997:997))
        (IOPATH i o (2820:2820:2820) (2782:2782:2782))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\toggle_pin\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (1800:1800:1800) (1560:1560:1560))
        (IOPATH i o (2803:2803:2803) (2767:2767:2767))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE \\clk\~input\\)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (748:748:748) (773:773:773))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE \\clk\~inputclkctrl\\)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (194:194:194) (190:190:190))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (364:364:364) (432:432:432))
        (PORT datab (357:357:357) (419:419:419))
        (PORT datad (317:317:317) (381:381:381))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH datab combout (406:406:406) (453:453:453))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (324:324:324) (381:381:381))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (227:227:227) (242:242:242))
        (PORT datad (839:839:839) (714:714:714))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (326:326:326) (388:388:388))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (328:328:328) (391:391:391))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (325:325:325) (382:382:382))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (325:325:325) (382:382:382))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (326:326:326) (382:382:382))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (269:269:269) (276:276:276))
        (PORT datad (839:839:839) (715:715:715))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (323:323:323) (380:380:380))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (327:327:327) (390:390:390))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (338:338:338) (393:393:393))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (340:340:340) (400:400:400))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1453:1453:1453) (1496:1496:1496))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (891:891:891) (785:785:785))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (377:377:377))
        (PORT datad (800:800:800) (677:677:677))
        (IOPATH dataa combout (374:374:374) (392:392:392))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (914:914:914) (829:829:829))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (748:748:748) (602:602:602))
        (PORT datad (305:305:305) (338:338:338))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (610:610:610) (569:569:569))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (473:473:473) (392:392:392))
        (PORT datad (303:303:303) (337:337:337))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (555:555:555) (544:544:544))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (346:346:346) (381:381:381))
        (PORT datad (465:465:465) (385:385:385))
        (IOPATH dataa combout (374:374:374) (392:392:392))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (338:338:338) (393:393:393))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (344:344:344) (404:404:404))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (504:504:504) (470:470:470))
        (PORT datad (230:230:230) (237:237:237))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (339:339:339) (394:394:394))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (340:340:340) (396:396:396))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (504:504:504) (470:470:470))
        (PORT datad (229:229:229) (236:236:236))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (503:503:503) (469:469:469))
        (PORT datad (228:228:228) (236:236:236))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (879:879:879) (806:806:806))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (817:817:817) (715:715:715))
        (PORT datad (833:833:833) (697:697:697))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1909:1909:1909) (1910:1910:1910))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (897:897:897) (823:823:823))
        (PORT datab (340:340:340) (396:396:396))
        (PORT datac (834:834:834) (763:763:763))
        (PORT datad (871:871:871) (802:802:802))
        (IOPATH dataa combout (349:349:349) (371:371:371))
        (IOPATH datab combout (354:354:354) (380:380:380))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (609:609:609) (566:566:566))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (439:439:439) (376:376:376))
        (PORT datad (303:303:303) (336:336:336))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (623:623:623) (577:577:577))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (343:343:343) (377:377:377))
        (PORT datad (432:432:432) (367:367:367))
        (IOPATH dataa combout (374:374:374) (392:392:392))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (562:562:562) (552:552:552))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (377:377:377))
        (PORT datad (433:433:433) (368:368:368))
        (IOPATH dataa combout (374:374:374) (392:392:392))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (300:300:300) (355:355:355))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1452:1452:1452) (1495:1495:1495))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (552:552:552) (542:542:542))
        (PORT datab (601:601:601) (550:550:550))
        (PORT datac (296:296:296) (360:360:360))
        (PORT datad (298:298:298) (353:353:353))
        (IOPATH dataa combout (377:377:377) (371:371:371))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (329:329:329) (392:392:392))
        (PORT datab (326:326:326) (383:383:383))
        (PORT datac (281:281:281) (347:347:347))
        (PORT datad (285:285:285) (343:343:343))
        (IOPATH dataa combout (392:392:392) (419:419:419))
        (IOPATH datab combout (393:393:393) (431:431:431))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (341:341:341) (400:400:400))
        (PORT datab (341:341:341) (397:397:397))
        (PORT datac (850:850:850) (780:780:780))
        (PORT datad (835:835:835) (770:770:770))
        (IOPATH dataa combout (408:408:408) (425:425:425))
        (IOPATH datab combout (415:415:415) (425:425:425))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (401:401:401))
        (PORT datab (542:542:542) (521:521:521))
        (PORT datac (501:501:501) (491:491:491))
        (PORT datad (297:297:297) (352:352:352))
        (IOPATH dataa combout (349:349:349) (371:371:371))
        (IOPATH datab combout (354:354:354) (380:380:380))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (328:328:328) (391:391:391))
        (PORT datab (324:324:324) (380:380:380))
        (PORT datac (283:283:283) (349:349:349))
        (PORT datad (285:285:285) (344:344:344))
        (IOPATH dataa combout (404:404:404) (450:450:450))
        (IOPATH datab combout (406:406:406) (453:453:453))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (858:858:858) (725:725:725))
        (PORT datab (268:268:268) (275:275:275))
        (PORT datac (225:225:225) (240:240:240))
        (PORT datad (799:799:799) (673:673:673))
        (IOPATH dataa combout (351:351:351) (371:371:371))
        (IOPATH datab combout (357:357:357) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (794:794:794) (686:686:686))
        (PORT datab (601:601:601) (551:551:551))
        (PORT datac (226:226:226) (241:241:241))
        (PORT datad (227:227:227) (235:235:235))
        (IOPATH dataa combout (377:377:377) (371:371:371))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[3\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1915:1915:1915) (1914:1914:1914))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (1232:1232:1232) (1139:1139:1139))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (363:363:363) (432:432:432))
        (PORT datab (360:360:360) (421:421:421))
        (PORT datad (317:317:317) (380:380:380))
        (IOPATH dataa combout (404:404:404) (450:450:450))
        (IOPATH datab combout (406:406:406) (453:453:453))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[1\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1915:1915:1915) (1914:1914:1914))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (1232:1232:1232) (1139:1139:1139))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (359:359:359) (425:425:425))
        (PORT datab (360:360:360) (421:421:421))
        (PORT datad (316:316:316) (379:379:379))
        (IOPATH dataa combout (420:420:420) (425:425:425))
        (IOPATH datab combout (406:406:406) (453:453:453))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[2\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1915:1915:1915) (1914:1914:1914))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (1232:1232:1232) (1139:1139:1139))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (364:364:364) (433:433:433))
        (PORT datab (360:360:360) (422:422:422))
        (PORT datad (317:317:317) (382:382:382))
        (IOPATH dataa combout (404:404:404) (450:450:450))
        (IOPATH datab combout (406:406:406) (453:453:453))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[0\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1915:1915:1915) (1914:1914:1914))
        (PORT d (90:90:90) (101:101:101))
        (PORT ena (1232:1232:1232) (1139:1139:1139))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
      (HOLD ena (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (324:324:324) (381:381:381))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (227:227:227) (242:242:242))
        (PORT datad (836:836:836) (721:721:721))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (328:328:328) (391:391:391))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (326:326:326) (388:388:388))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (325:325:325) (382:382:382))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (339:339:339) (394:394:394))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (339:339:339) (394:394:394))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (908:908:908) (841:841:841))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (358:358:358) (388:388:388))
        (PORT datad (816:816:816) (691:691:691))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1444:1444:1444) (1486:1486:1486))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (341:341:341) (401:401:401))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (323:323:323) (380:380:380))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (326:326:326) (389:389:389))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (322:322:322) (378:378:378))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (328:328:328) (385:385:385))
        (IOPATH datab combout (423:423:423) (451:451:451))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (228:228:228) (244:244:244))
        (PORT datad (836:836:836) (721:721:721))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1447:1447:1447) (1488:1488:1488))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (548:548:548) (522:522:522))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (489:489:489) (416:416:416))
        (PORT datad (317:317:317) (349:349:349))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1444:1444:1444) (1486:1486:1486))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (542:542:542) (528:528:528))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (358:358:358) (388:388:388))
        (PORT datad (443:443:443) (383:383:383))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1444:1444:1444) (1486:1486:1486))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (543:543:543) (522:522:522))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (355:355:355) (385:385:385))
        (PORT datad (481:481:481) (410:410:410))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1912:1912:1912) (1911:1911:1911))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (341:341:341) (401:401:401))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1446:1446:1446) (1487:1487:1487))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (343:343:343) (403:403:403))
        (IOPATH dataa combout (435:435:435) (425:425:425))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (227:227:227) (243:243:243))
        (PORT datad (513:513:513) (450:450:450))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1913:1913:1913) (1905:1905:1905))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (341:341:341) (401:401:401))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1446:1446:1446) (1487:1487:1487))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (340:340:340) (395:395:395))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (574:574:574) (491:491:491))
        (PORT datad (228:228:228) (236:236:236))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1913:1913:1913) (1905:1905:1905))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (555:555:555) (533:533:533))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (487:487:487) (414:414:414))
        (PORT datad (315:315:315) (347:347:347))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1912:1912:1912) (1911:1911:1911))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (822:822:822) (733:733:733))
        (PORT datab (340:340:340) (395:395:395))
        (PORT datac (512:512:512) (505:505:505))
        (PORT datad (843:843:843) (729:729:729))
        (IOPATH dataa combout (349:349:349) (371:371:371))
        (IOPATH datab combout (354:354:354) (380:380:380))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (543:543:543) (523:523:523))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (356:356:356) (386:386:386))
        (PORT datad (438:438:438) (376:376:376))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1912:1912:1912) (1911:1911:1911))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (341:341:341) (401:401:401))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (574:574:574) (491:491:491))
        (PORT datad (227:227:227) (234:234:234))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1913:1913:1913) (1905:1905:1905))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (598:598:598) (547:547:547))
        (IOPATH datab combout (437:437:437) (425:425:425))
        (IOPATH datab cout (497:497:497) (381:381:381))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (437:437:437) (383:383:383))
        (PORT datad (313:313:313) (345:345:345))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1912:1912:1912) (1911:1911:1911))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (IOPATH dataa combout (414:414:414) (444:444:444))
        (IOPATH dataa cout (486:486:486) (375:375:375))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
        (IOPATH cin cout (63:63:63) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1913:1913:1913) (1905:1905:1905))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (539:539:539) (507:507:507))
        (IOPATH datad combout (167:167:167) (143:143:143))
        (IOPATH cin combout (549:549:549) (519:519:519))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (356:356:356) (386:386:386))
        (PORT datad (474:474:474) (399:399:399))
        (IOPATH datab combout (384:384:384) (398:398:398))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1912:1912:1912) (1911:1911:1911))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (342:342:342) (402:402:402))
        (PORT datab (339:339:339) (394:394:394))
        (PORT datac (776:776:776) (691:691:691))
        (PORT datad (547:547:547) (523:523:523))
        (IOPATH dataa combout (394:394:394) (400:400:400))
        (IOPATH datab combout (400:400:400) (391:391:391))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (934:934:934) (866:866:866))
        (PORT datab (339:339:339) (394:394:394))
        (PORT datac (1116:1116:1116) (976:976:976))
        (PORT datad (1169:1169:1169) (1012:1012:1012))
        (IOPATH dataa combout (428:428:428) (449:449:449))
        (IOPATH datab combout (437:437:437) (407:407:407))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (341:341:341) (401:401:401))
        (PORT datab (339:339:339) (394:394:394))
        (PORT datac (299:299:299) (363:363:363))
        (PORT datad (796:796:796) (697:697:697))
        (IOPATH dataa combout (394:394:394) (400:400:400))
        (IOPATH datab combout (400:400:400) (391:391:391))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (328:328:328) (391:391:391))
        (PORT datab (324:324:324) (380:380:380))
        (PORT datac (283:283:283) (349:349:349))
        (PORT datad (285:285:285) (344:344:344))
        (IOPATH dataa combout (404:404:404) (450:450:450))
        (IOPATH datab combout (406:406:406) (453:453:453))
        (IOPATH datac combout (301:301:301) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (330:330:330) (393:393:393))
        (PORT datab (326:326:326) (383:383:383))
        (PORT datac (281:281:281) (347:347:347))
        (PORT datad (286:286:286) (344:344:344))
        (IOPATH dataa combout (392:392:392) (419:419:419))
        (IOPATH datab combout (393:393:393) (431:431:431))
        (IOPATH datac combout (305:305:305) (283:283:283))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (271:271:271) (283:283:283))
        (PORT datab (269:269:269) (276:276:276))
        (PORT datac (812:812:812) (693:693:693))
        (PORT datad (1118:1118:1118) (895:895:895))
        (IOPATH dataa combout (351:351:351) (371:371:371))
        (IOPATH datab combout (357:357:357) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (272:272:272) (283:283:283))
        (PORT datab (340:340:340) (395:395:395))
        (PORT datac (225:225:225) (241:241:241))
        (PORT datad (227:227:227) (234:234:234))
        (IOPATH dataa combout (351:351:351) (371:371:371))
        (IOPATH datab combout (357:357:357) (380:380:380))
        (IOPATH datac combout (301:301:301) (285:285:285))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_pin\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (318:318:318) (350:350:350))
        (IOPATH datac combout (415:415:415) (429:429:429))
        (IOPATH datad combout (167:167:167) (143:143:143))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_pin\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (1912:1912:1912) (1911:1911:1911))
        (PORT d (90:90:90) (101:101:101))
        (IOPATH (posedge clk) q (240:240:240) (240:240:240))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (195:195:195))
    )
  )
)
