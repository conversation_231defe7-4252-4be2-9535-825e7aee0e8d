{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1753939405744 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1753939405747 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 13:23:25 2025 " "Processing started: Thu Jul 31 13:23:25 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1753939405747 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1753939405747 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off LED -c LED " "Command: quartus_map --read_settings_files=on --write_settings_files=off LED -c LED" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1753939405747 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Analysis & Synthesis" 0 -1 1753939405933 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1753939405933 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "led.v 1 1 " "Found 1 design units, including 1 entities, in source file led.v" { { "Info" "ISGN_ENTITY_NAME" "1 LED " "Found entity 1: LED" {  } { { "LED.v" "" { Text "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1753939411531 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1753939411531 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "LED " "Elaborating entity \"LED\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1753939411544 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 25 LED.v(12) " "Verilog HDL assignment warning at LED.v(12): truncated value with size 32 to match size of target (25)" {  } { { "LED.v" "" { Text "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v" 12 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1753939411544 "|LED"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 25 LED.v(13) " "Verilog HDL assignment warning at LED.v(13): truncated value with size 32 to match size of target (25)" {  } { { "LED.v" "" { Text "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v" 13 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1753939411544 "|LED"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "8 4 LED.v(21) " "Verilog HDL assignment warning at LED.v(21): truncated value with size 8 to match size of target (4)" {  } { { "LED.v" "" { Text "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v" 21 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1753939411545 "|LED"}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Analysis & Synthesis" 0 -1 1753939411827 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "0 0 0 0 0 " "Adding 0 node(s), including 0 DDIO, 0 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Design Software" 0 -1 1753939412100 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1753939412100 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "103 " "Implemented 103 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "1 " "Implemented 1 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Design Software" 0 -1 1753939412117 ""} { "Info" "ICUT_CUT_TM_OPINS" "5 " "Implemented 5 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Design Software" 0 -1 1753939412117 ""} { "Info" "ICUT_CUT_TM_LCELLS" "97 " "Implemented 97 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Design Software" 0 -1 1753939412117 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Analysis & Synthesis" 0 -1 1753939412117 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 4 s Quartus Prime " "Quartus Prime Analysis & Synthesis was successful. 0 errors, 4 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4830 " "Peak virtual memory: 4830 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1753939412134 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 13:23:32 2025 " "Processing ended: Thu Jul 31 13:23:32 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1753939412134 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:07 " "Elapsed time: 00:00:07" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1753939412134 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:13 " "Total CPU time (on all processors): 00:00:13" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1753939412134 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1753939412134 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Analysis & Synthesis" 0 -1 1753939413029 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Fitter Quartus Prime " "Running Quartus Prime Fitter" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1753939413032 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 13:23:32 2025 " "Processing started: Thu Jul 31 13:23:32 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1753939413032 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Fitter" 0 -1 1753939413032 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_fit --read_settings_files=off --write_settings_files=off LED -c LED " "Command: quartus_fit --read_settings_files=off --write_settings_files=off LED -c LED" {  } {  } 0 0 "Command: %1!s!" 0 0 "Fitter" 0 -1 1753939413032 ""}
{ "Info" "0" "" "qfit2_default_script.tcl version: #1" {  } {  } 0 0 "qfit2_default_script.tcl version: #1" 0 0 "Fitter" 0 0 1753939413071 ""}
{ "Info" "0" "" "Project  = LED" {  } {  } 0 0 "Project  = LED" 0 0 "Fitter" 0 0 1753939413072 ""}
{ "Info" "0" "" "Revision = LED" {  } {  } 0 0 "Revision = LED" 0 0 "Fitter" 0 0 1753939413072 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Fitter" 0 -1 1753939413113 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Fitter" 0 -1 1753939413113 ""}
{ "Info" "IMPP_MPP_USER_DEVICE" "LED EP4CE6E22C8L " "Selected device EP4CE6E22C8L for design \"LED\"" {  } {  } 0 119006 "Selected device %2!s! for design \"%1!s!\"" 0 0 "Fitter" 0 -1 1753939413117 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1753939413151 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1753939413151 ""}
{ "Info" "IFITCC_FITCC_INFO_AUTO_FIT_COMPILATION_ON" "" "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" {  } {  } 0 171003 "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" 0 0 "Fitter" 0 -1 1753939413205 ""}
{ "Warning" "WCPT_FEATURE_DISABLED_POST" "LogicLock " "Feature LogicLock is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." {  } {  } 0 292013 "Feature %1!s! is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." 0 0 "Fitter" 0 -1 1753939413207 ""}
{ "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED" "" "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" { { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE10E22C8L " "Device EP4CE10E22C8L is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1753939413256 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE10E22I8L " "Device EP4CE10E22I8L is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1753939413256 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE6E22I8L " "Device EP4CE6E22I8L is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1753939413256 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE15E22C8L " "Device EP4CE15E22C8L is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1753939413256 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE15E22I8L " "Device EP4CE15E22I8L is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1753939413256 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22E22C8L " "Device EP4CE22E22C8L is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1753939413256 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22E22I8L " "Device EP4CE22E22I8L is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1753939413256 ""}  } {  } 2 176444 "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" 0 0 "Fitter" 0 -1 1753939413256 ""}
{ "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION" "5 " "Fitter converted 5 user pins into dedicated programming pins" { { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_ASDO_DATA1~ 6 " "Pin ~ALTERA_ASDO_DATA1~ is reserved at location 6" {  } { { "g:/altera/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_ASDO_DATA1~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/" { { 0 { 0 ""} 0 229 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1753939413257 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_FLASH_nCE_nCSO~ 8 " "Pin ~ALTERA_FLASH_nCE_nCSO~ is reserved at location 8" {  } { { "g:/altera/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_FLASH_nCE_nCSO~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/" { { 0 { 0 ""} 0 231 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1753939413257 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DCLK~ 12 " "Pin ~ALTERA_DCLK~ is reserved at location 12" {  } { { "g:/altera/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DCLK~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/" { { 0 { 0 ""} 0 233 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1753939413257 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DATA0~ 13 " "Pin ~ALTERA_DATA0~ is reserved at location 13" {  } { { "g:/altera/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DATA0~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/" { { 0 { 0 ""} 0 235 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1753939413257 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_nCEO~ 101 " "Pin ~ALTERA_nCEO~ is reserved at location 101" {  } { { "g:/altera/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_nCEO~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/" { { 0 { 0 ""} 0 237 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1753939413257 ""}  } {  } 0 169124 "Fitter converted %1!d! user pins into dedicated programming pins" 0 0 "Fitter" 0 -1 1753939413257 ""}
{ "Warning" "WCUT_CUT_ATOM_PINS_WITH_INCOMPLETE_IO_ASSIGNMENTS" "" "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" {  } {  } 0 15714 "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" 0 0 "Fitter" 0 -1 1753939413258 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "LED.sdc " "Synopsys Design Constraints File file not found: 'LED.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Fitter" 0 -1 1753939413476 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_NO_DERIVING_MSG" "base clocks " "No user constrained base clocks found in the design" {  } {  } 0 332144 "No user constrained %1!s! found in the design" 0 0 "Fitter" 0 -1 1753939413476 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Fitter" 0 -1 1753939413477 ""}
{ "Info" "ISTA_NO_UNCERTAINTY_FOUND" "" "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." {  } {  } 0 332154 "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." 0 0 "Fitter" 0 -1 1753939413477 ""}
{ "Info" "ISTA_TDC_NO_DEFAULT_OPTIMIZATION_GOALS" "" "Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." {  } {  } 0 332130 "Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." 0 0 "Fitter" 0 -1 1753939413477 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "clk~input (placed in PIN 88 (CLK7, DIFFCLK_3n)) " "Automatically promoted node clk~input (placed in PIN 88 (CLK7, DIFFCLK_3n))" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G8 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G8" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1753939413483 ""}  } { { "LED.v" "" { Text "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/LED.v" 4 0 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/" { { 0 { 0 ""} 0 224 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753939413483 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_START_REGPACKING_INFO" "" "Starting register packing" {  } {  } 0 176233 "Starting register packing" 0 0 "Fitter" 0 -1 1753939413578 ""}
{ "Extra Info" "IFSAC_FSAC_START_REG_LOCATION_PROCESSING" "" "Performing register packing on registers with non-logic cell location assignments" {  } {  } 1 176273 "Performing register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1753939413578 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_REG_LOCATION_PROCESSING" "" "Completed register packing on registers with non-logic cell location assignments" {  } {  } 1 176274 "Completed register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1753939413578 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_BEGIN_FAST_REGISTER_INFO" "" "Started Fast Input/Output/OE register processing" {  } {  } 1 176236 "Started Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1753939413578 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_FAST_REGISTER_INFO" "" "Finished Fast Input/Output/OE register processing" {  } {  } 1 176237 "Finished Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1753939413578 ""}
{ "Extra Info" "IFSAC_FSAC_START_MAC_SCAN_CHAIN_INFERENCING" "" "Start inferring scan chains for DSP blocks" {  } {  } 1 176238 "Start inferring scan chains for DSP blocks" 1 0 "Fitter" 0 -1 1753939413578 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_MAC_SCAN_CHAIN_INFERENCING" "" "Inferring scan chains for DSP blocks is complete" {  } {  } 1 176239 "Inferring scan chains for DSP blocks is complete" 1 0 "Fitter" 0 -1 1753939413578 ""}
{ "Extra Info" "IFSAC_FSAC_START_IO_MULT_RAM_PACKING" "" "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" {  } {  } 1 176248 "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" 1 0 "Fitter" 0 -1 1753939413579 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_IO_MULT_RAM_PACKING" "" "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" {  } {  } 1 176249 "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" 1 0 "Fitter" 0 -1 1753939413585 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_REGPACKING_INFO" "" "Finished register packing" { { "Extra Info" "IFSAC_NO_REGISTERS_WERE_PACKED" "" "No registers were packed into other blocks" {  } {  } 1 176219 "No registers were packed into other blocks" 0 0 "Design Software" 0 -1 1753939413585 ""}  } {  } 0 176235 "Finished register packing" 0 0 "Fitter" 0 -1 1753939413585 ""}
{ "Info" "IFITCC_FITTER_PREPARATION_END" "00:00:00 " "Fitter preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 171121 "Fitter preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1753939413589 ""}
{ "Info" "IVPR20K_VPR_FAMILY_APL_ERROR" "" "Fitter has disabled Advanced Physical Optimization because it is not supported for the current family." {  } {  } 0 14896 "Fitter has disabled Advanced Physical Optimization because it is not supported for the current family." 0 0 "Fitter" 0 -1 1753939413591 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_START" "" "Fitter placement preparation operations beginning" {  } {  } 0 170189 "Fitter placement preparation operations beginning" 0 0 "Fitter" 0 -1 1753939413830 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_END" "00:00:00 " "Fitter placement preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 170190 "Fitter placement preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1753939413853 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_START" "" "Fitter placement operations beginning" {  } {  } 0 170191 "Fitter placement operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_INFO_VPR_PLACEMENT_FINISH" "" "Fitter placement was successful" {  } {  } 0 170137 "Fitter placement was successful" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_END" "00:00:00 " "Fitter placement operations ending: elapsed time is 00:00:00" {  } {  } 0 170192 "Fitter placement operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_START" "" "Fitter routing operations beginning" {  } {  } 0 170193 "Fitter routing operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_PERCENT_ROUTING_RESOURCE_USAGE" "0 " "Router estimated average interconnect usage is 0% of the available device resources" { { "Info" "IFITAPI_FITAPI_VPR_PEAK_ROUTING_REGION" "0 X23_Y12 X34_Y24 " "Router estimated peak interconnect usage is 0% of the available device resources in the region that extends from location X23_Y12 to location X34_Y24" {  } { { "loc" "" { Generic "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/" { { 1 { 0 "Router estimated peak interconnect usage is 0% of the available device resources in the region that extends from location X23_Y12 to location X34_Y24"} { { 12 { 0 ""} 23 12 12 13 }  }  }  }  } }  } 0 170196 "Router estimated peak interconnect usage is %1!d!%% of the available device resources in the region that extends from location %2!s! to location %3!s!" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 170195 "Router estimated average interconnect usage is %1!d!%% of the available device resources" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED" "" "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." { { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_ROUTABILITY" "" "Optimizations that may affect the design's routability were skipped" {  } {  } 0 170201 "Optimizations that may affect the design's routability were skipped" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 170199 "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_END" "00:00:00 " "Fitter routing operations ending: elapsed time is 00:00:00" {  } {  } 0 170194 "Fitter routing operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_TIMING_ANALYSIS_TIME" "the Fitter 0.07 " "Total time spent on timing analysis during the Fitter is 0.07 seconds." {  } {  } 0 11888 "Total time spent on timing analysis during %1!s! is %2!s! seconds." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1753939414622 ""}
{ "Info" "IFITCC_FITTER_POST_OPERATION_END" "00:00:00 " "Fitter post-fit operations ending: elapsed time is 00:00:00" {  } {  } 0 11218 "Fitter post-fit operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1753939414814 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/output_files/LED.fit.smsg " "Generated suppressed messages file C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/output_files/LED.fit.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Fitter" 0 -1 1753939414964 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Fitter 0 s 4 s Quartus Prime " "Quartus Prime Fitter was successful. 0 errors, 4 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "6295 " "Peak virtual memory: 6295 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1753939415119 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 13:23:35 2025 " "Processing ended: Thu Jul 31 13:23:35 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1753939415119 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:03 " "Elapsed time: 00:00:03" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1753939415119 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:04 " "Total CPU time (on all processors): 00:00:04" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1753939415119 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Fitter" 0 -1 1753939415119 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Fitter" 0 -1 1753939415903 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Assembler Quartus Prime " "Running Quartus Prime Assembler" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1753939415906 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 13:23:35 2025 " "Processing started: Thu Jul 31 13:23:35 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1753939415906 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Assembler" 0 -1 1753939415906 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_asm --read_settings_files=off --write_settings_files=off LED -c LED " "Command: quartus_asm --read_settings_files=off --write_settings_files=off LED -c LED" {  } {  } 0 0 "Command: %1!s!" 0 0 "Assembler" 0 -1 1753939415906 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Assembler" 0 -1 1753939416029 ""}
{ "Info" "IASM_ASM_GENERATING_POWER_DATA" "" "Writing out detailed assembly data for power analysis" {  } {  } 0 115031 "Writing out detailed assembly data for power analysis" 0 0 "Assembler" 0 -1 1753939416168 ""}
{ "Info" "IASM_ASM_GENERATING_PROGRAMMING_FILES" "" "Assembler is generating device programming files" {  } {  } 0 115030 "Assembler is generating device programming files" 0 0 "Assembler" 0 -1 1753939416176 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Assembler 0 s 1  Quartus Prime " "Quartus Prime Assembler was successful. 0 errors, 1 warning" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4673 " "Peak virtual memory: 4673 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1753939416240 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 13:23:36 2025 " "Processing ended: Thu Jul 31 13:23:36 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1753939416240 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1753939416240 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:00 " "Total CPU time (on all processors): 00:00:00" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1753939416240 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Assembler" 0 -1 1753939416240 ""}
{ "Info" "IFLOW_DISABLED_MODULE" "Power Analyzer FLOW_ENABLE_POWER_ANALYZER " "Skipped module Power Analyzer due to the assignment FLOW_ENABLE_POWER_ANALYZER" {  } {  } 0 293026 "Skipped module %1!s! due to the assignment %2!s!" 0 0 "Assembler" 0 -1 1753939416800 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Assembler" 0 -1 1753939417061 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Timing Analyzer Quartus Prime " "Running Quartus Prime Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1753939417065 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 13:23:36 2025 " "Processing started: Thu Jul 31 13:23:36 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1753939417065 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Timing Analyzer" 0 -1 1753939417065 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta LED -c LED " "Command: quartus_sta LED -c LED" {  } {  } 0 0 "Command: %1!s!" 0 0 "Timing Analyzer" 0 -1 1753939417065 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Timing Analyzer" 0 0 1753939417108 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Timing Analyzer" 0 -1 1753939417174 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Timing Analyzer" 0 -1 1753939417174 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417207 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417207 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "LED.sdc " "Synopsys Design Constraints File file not found: 'LED.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Timing Analyzer" 0 -1 1753939417295 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "base clocks \"derive_clocks -period 1.0\" " "No user constrained base clocks found in the design. Calling \"derive_clocks -period 1.0\"" {  } {  } 0 332142 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417296 ""}
{ "Info" "ISTA_DERIVE_CLOCKS_INFO" "Deriving Clocks " "Deriving Clocks" { { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name clk clk " "create_clock -period 1.000 -name clk clk" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1753939417296 ""}  } {  } 0 332105 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1753939417296 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Timing Analyzer" 0 -1 1753939417296 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1753939417297 ""}
{ "Info" "0" "" "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Timing Analyzer" 0 0 1753939417297 ""}
{ "Info" "0" "" "Analyzing Slow 1000mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1000mV 85C Model" 0 0 "Timing Analyzer" 0 0 1753939417300 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1753939417307 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1753939417307 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -3.143 " "Worst-case setup slack is -3.143" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417308 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417308 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.143            -128.690 clk  " "   -3.143            -128.690 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417308 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417308 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.513 " "Worst-case hold slack is 0.513" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417310 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417310 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.513               0.000 clk  " "    0.513               0.000 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417310 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417310 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753939417312 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753939417314 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417316 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417316 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -99.965 clk  " "   -3.000             -99.965 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417316 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417316 ""}
{ "Warning" "WSTA_METASTABILITY_REPORT_DISALLOW_GLOBAL_OFF" "" "Ignoring Synchronizer Identification setting Off, and using Auto instead." {  } {  } 0 18330 "Ignoring Synchronizer Identification setting Off, and using Auto instead." 0 0 "Timing Analyzer" 0 -1 1753939417322 ""}
{ "Info" "0" "" "Analyzing Slow 1000mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1000mV 0C Model" 0 0 "Timing Analyzer" 0 0 1753939417324 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Timing Analyzer" 0 -1 1753939417334 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Timing Analyzer" 0 -1 1753939417414 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1753939417437 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1753939417439 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1753939417439 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -2.986 " "Worst-case setup slack is -2.986" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417441 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417441 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.986            -122.766 clk  " "   -2.986            -122.766 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417441 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417441 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.496 " "Worst-case hold slack is 0.496" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417443 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417443 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.496               0.000 clk  " "    0.496               0.000 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417443 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417443 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753939417444 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753939417446 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417447 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417447 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -99.965 clk  " "   -3.000             -99.965 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417447 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417447 ""}
{ "Warning" "WSTA_METASTABILITY_REPORT_DISALLOW_GLOBAL_OFF" "" "Ignoring Synchronizer Identification setting Off, and using Auto instead." {  } {  } 0 18330 "Ignoring Synchronizer Identification setting Off, and using Auto instead." 0 0 "Timing Analyzer" 0 -1 1753939417454 ""}
{ "Info" "0" "" "Analyzing Fast 1000mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1000mV 0C Model" 0 0 "Timing Analyzer" 0 0 1753939417456 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1753939417513 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1753939417513 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1753939417513 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -1.333 " "Worst-case setup slack is -1.333" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417515 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417515 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.333             -48.039 clk  " "   -1.333             -48.039 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417515 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417515 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.275 " "Worst-case hold slack is 0.275" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417517 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417517 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.275               0.000 clk  " "    0.275               0.000 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417517 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417517 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753939417519 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1753939417521 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417523 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417523 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -99.965 clk  " "   -3.000             -99.965 clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1753939417523 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1753939417523 ""}
{ "Warning" "WSTA_METASTABILITY_REPORT_DISALLOW_GLOBAL_OFF" "" "Ignoring Synchronizer Identification setting Off, and using Auto instead." {  } {  } 0 18330 "Ignoring Synchronizer Identification setting Off, and using Auto instead." 0 0 "Timing Analyzer" 0 -1 1753939417529 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1753939417719 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1753939417719 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Timing Analyzer 0 s 8 s Quartus Prime " "Quartus Prime Timing Analyzer was successful. 0 errors, 8 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4868 " "Peak virtual memory: 4868 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1753939417743 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 13:23:37 2025 " "Processing ended: Thu Jul 31 13:23:37 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1753939417743 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1753939417743 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1753939417743 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Timing Analyzer" 0 -1 1753939417743 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Timing Analyzer" 0 -1 1753939418546 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "EDA Netlist Writer Quartus Prime " "Running Quartus Prime EDA Netlist Writer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1753939418549 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 13:23:38 2025 " "Processing started: Thu Jul 31 13:23:38 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1753939418549 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "EDA Netlist Writer" 0 -1 1753939418549 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_eda --read_settings_files=off --write_settings_files=off LED -c LED " "Command: quartus_eda --read_settings_files=off --write_settings_files=off LED -c LED" {  } {  } 0 0 "Command: %1!s!" 0 0 "EDA Netlist Writer" 0 -1 1753939418550 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "EDA Netlist Writer" 0 -1 1753939418736 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "LED_8l_1000mv_85c_slow.vho C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/ simulation " "Generated file LED_8l_1000mv_85c_slow.vho in folder \"C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "EDA Netlist Writer" 0 -1 1753939418784 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "LED_8l_1000mv_0c_slow.vho C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/ simulation " "Generated file LED_8l_1000mv_0c_slow.vho in folder \"C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "EDA Netlist Writer" 0 -1 1753939418798 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "LED_min_1000mv_0c_fast.vho C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/ simulation " "Generated file LED_min_1000mv_0c_fast.vho in folder \"C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "EDA Netlist Writer" 0 -1 1753939418811 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "LED.vho C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/ simulation " "Generated file LED.vho in folder \"C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "EDA Netlist Writer" 0 -1 1753939418826 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "LED_8l_1000mv_85c_vhd_slow.sdo C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/ simulation " "Generated file LED_8l_1000mv_85c_vhd_slow.sdo in folder \"C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "EDA Netlist Writer" 0 -1 1753939418836 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "LED_8l_1000mv_0c_vhd_slow.sdo C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/ simulation " "Generated file LED_8l_1000mv_0c_vhd_slow.sdo in folder \"C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "EDA Netlist Writer" 0 -1 1753939418845 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "LED_min_1000mv_0c_vhd_fast.sdo C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/ simulation " "Generated file LED_min_1000mv_0c_vhd_fast.sdo in folder \"C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "EDA Netlist Writer" 0 -1 1753939418855 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "LED_vhd.sdo C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/ simulation " "Generated file LED_vhd.sdo in folder \"C:/Users/<USER>/Downloads/zuolan_v4.1/CXMCC-C-LED/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "EDA Netlist Writer" 0 -1 1753939418866 ""}
{ "Info" "IQEXE_ERROR_COUNT" "EDA Netlist Writer 0 s 1  Quartus Prime " "Quartus Prime EDA Netlist Writer was successful. 0 errors, 1 warning" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4640 " "Peak virtual memory: 4640 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1753939418884 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 13:23:38 2025 " "Processing ended: Thu Jul 31 13:23:38 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1753939418884 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:00 " "Elapsed time: 00:00:00" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1753939418884 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:00 " "Total CPU time (on all processors): 00:00:00" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1753939418884 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "EDA Netlist Writer" 0 -1 1753939418884 ""}
{ "Info" "IFLOW_ERROR_COUNT" "Full Compilation 0 s 18 s " "Quartus Prime Full Compilation was successful. 0 errors, 18 warnings" {  } {  } 0 293000 "Quartus Prime %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "EDA Netlist Writer" 0 -1 1753939419444 ""}
