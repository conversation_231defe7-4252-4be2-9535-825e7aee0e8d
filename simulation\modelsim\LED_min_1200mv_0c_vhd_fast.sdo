// Copyright (C) 2018  Intel Corporation. All rights reserved.
// Your use of Intel Corporation's design tools, logic functions 
// and other software and tools, and its AMPP partner logic 
// functions, and any output files from any of the foregoing 
// (including device programming or simulation files), and any 
// associated documentation or information are expressly subject 
// to the terms and conditions of the Intel Program License 
// Subscription Agreement, the Intel Quartus Prime License Agreement,
// the Intel FPGA IP License Agreement, or other applicable license
// agreement, including, without limitation, that your use is for
// the sole purpose of programming logic devices manufactured by
// Intel and sold by Intel or its authorized distributors.  Please
// refer to the applicable agreement for further details.


// 
// Device: Altera EP4CE6E22C8 Package TQFP144
// 

//
// This file contains Fast Corner delays for the design using part EP4CE6E22C8,
// with speed grade M, core voltage 1.2VmV, and temperature 0 Celsius
//

// 
// This SDF file should be used for ModelSim-Altera (VHDL) only
// 

(DELAYFILE
  (SDFVERSION "2.1")
  (DESIGN "LED")
  (DATE "07/31/2025 14:08:29")
  (VENDOR "Altera")
  (PROGRAM "Quartus Prime")
  (VERSION "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition")
  (DIVIDER .)
  (TIMESCALE 1 ps)

  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[0\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (415:415:415) (467:467:467))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[1\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (454:454:454) (521:521:521))
        (IOPATH i o (1565:1565:1565) (1570:1570:1570))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[2\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (457:457:457) (523:523:523))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\led\[3\]\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (466:466:466) (533:533:533))
        (IOPATH i o (1619:1619:1619) (1644:1644:1644))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_obuf")
    (INSTANCE \\toggle_pin\~output\\)
    (DELAY
      (ABSOLUTE
        (PORT i (766:766:766) (880:880:880))
        (IOPATH i o (1612:1612:1612) (1615:1615:1615))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_io_ibuf")
    (INSTANCE \\clk\~input\\)
    (DELAY
      (ABSOLUTE
        (IOPATH i o (318:318:318) (698:698:698))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_clkctrl")
    (INSTANCE \\clk\~inputclkctrl\\)
    (DELAY
      (ABSOLUTE
        (PORT inclk[0] (108:108:108) (89:89:89))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (154:154:154) (210:210:210))
        (PORT datab (151:151:151) (203:203:203))
        (PORT datad (139:139:139) (181:181:181))
        (IOPATH dataa combout (170:170:170) (163:163:163))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (135:135:135) (185:185:185))
        (IOPATH datab combout (192:192:192) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (90:90:90) (111:111:111))
        (PORT datad (326:326:326) (379:379:379))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (759:759:759) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (135:135:135) (186:186:186))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (759:759:759) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (136:136:136) (187:187:187))
        (IOPATH dataa combout (186:186:186) (175:175:175))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (759:759:759) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (134:134:134) (184:184:184))
        (IOPATH datab combout (166:166:166) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (759:759:759) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (134:134:134) (184:184:184))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (759:759:759) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (136:136:136) (186:186:186))
        (IOPATH datab combout (166:166:166) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (105:105:105) (134:134:134))
        (PORT datad (326:326:326) (379:379:379))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (759:759:759) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (134:134:134) (182:182:182))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (759:759:759) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (135:135:135) (187:187:187))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (759:759:759) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (141:141:141) (189:189:189))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (759:759:759) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (142:142:142) (192:192:192))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (759:759:759) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (341:341:341) (410:410:410))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (145:145:145) (186:186:186))
        (PORT datad (312:312:312) (361:361:361))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (359:359:359) (436:436:436))
        (IOPATH datab combout (166:166:166) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (279:279:279) (314:314:314))
        (PORT datad (135:135:135) (165:165:165))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (230:230:230) (287:287:287))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (170:170:170) (202:202:202))
        (PORT datad (133:133:133) (164:164:164))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (216:216:216) (274:274:274))
        (IOPATH datab combout (166:166:166) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (149:149:149) (190:190:190))
        (PORT datad (169:169:169) (198:198:198))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (141:141:141) (189:189:189))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (776:776:776))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (145:145:145) (197:197:197))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (201:201:201) (242:242:242))
        (PORT datad (93:93:93) (111:111:111))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (776:776:776))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (142:142:142) (189:189:189))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (776:776:776))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (144:144:144) (193:193:193))
        (IOPATH datab combout (166:166:166) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (200:200:200) (242:242:242))
        (PORT datad (92:92:92) (110:110:110))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (776:776:776))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (144:144:144) (196:196:196))
        (IOPATH dataa combout (186:186:186) (175:175:175))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (200:200:200) (241:241:241))
        (PORT datad (92:92:92) (109:109:109))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (776:776:776))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (360:360:360) (428:428:428))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (334:334:334) (384:384:384))
        (PORT datad (327:327:327) (380:380:380))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (954:954:954) (995:995:995))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (366:366:366) (439:439:439))
        (PORT datab (143:143:143) (192:192:192))
        (PORT datac (340:340:340) (402:402:402))
        (PORT datad (356:356:356) (425:425:425))
        (IOPATH dataa combout (158:158:158) (163:163:163))
        (IOPATH datab combout (160:160:160) (167:167:167))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (228:228:228) (285:285:285))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (161:161:161) (189:189:189))
        (PORT datad (132:132:132) (163:163:163))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (234:234:234) (290:290:290))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (145:145:145) (186:186:186))
        (PORT datad (159:159:159) (186:186:186))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (142:142:142) (190:190:190))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (776:776:776))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (218:218:218) (278:278:278))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (145:145:145) (186:186:186))
        (PORT datad (160:160:160) (187:187:187))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (777:777:777))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add0\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (129:129:129) (166:166:166))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (758:758:758) (776:776:776))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (213:213:213) (272:272:272))
        (PORT datab (226:226:226) (279:279:279))
        (PORT datac (128:128:128) (169:169:169))
        (PORT datad (129:129:129) (166:166:166))
        (IOPATH dataa combout (166:166:166) (157:157:157))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (137:137:137) (191:191:191))
        (PORT datab (135:135:135) (185:185:185))
        (PORT datac (120:120:120) (163:163:163))
        (PORT datad (123:123:123) (162:162:162))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (142:142:142) (193:193:193))
        (PORT datab (144:144:144) (193:193:193))
        (PORT datac (346:346:346) (409:409:409))
        (PORT datad (341:341:341) (403:403:403))
        (IOPATH dataa combout (170:170:170) (163:163:163))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (120:120:120) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (142:142:142) (193:193:193))
        (PORT datab (208:208:208) (262:262:262))
        (PORT datac (197:197:197) (245:245:245))
        (PORT datad (128:128:128) (166:166:166))
        (IOPATH dataa combout (158:158:158) (163:163:163))
        (IOPATH datab combout (160:160:160) (167:167:167))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (136:136:136) (189:189:189))
        (PORT datab (134:134:134) (184:184:184))
        (PORT datac (121:121:121) (164:164:164))
        (PORT datad (122:122:122) (161:161:161))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (120:120:120) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (330:330:330) (387:387:387))
        (PORT datab (103:103:103) (132:132:132))
        (PORT datac (90:90:90) (111:111:111))
        (PORT datad (312:312:312) (360:360:360))
        (IOPATH dataa combout (159:159:159) (163:163:163))
        (IOPATH datab combout (161:161:161) (167:167:167))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal0\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (319:319:319) (373:373:373))
        (PORT datab (226:226:226) (279:279:279))
        (PORT datac (90:90:90) (111:111:111))
        (PORT datad (91:91:91) (109:109:109))
        (IOPATH dataa combout (166:166:166) (157:157:157))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[3\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (1000:1000:1000))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (506:506:506) (536:536:536))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (154:154:154) (209:209:209))
        (PORT datab (153:153:153) (204:204:204))
        (PORT datad (139:139:139) (180:180:180))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[1\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (1000:1000:1000))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (506:506:506) (536:536:536))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\led\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (152:152:152) (206:206:206))
        (PORT datab (152:152:152) (204:204:204))
        (PORT datad (138:138:138) (180:180:180))
        (IOPATH dataa combout (170:170:170) (163:163:163))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[2\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (1000:1000:1000))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (506:506:506) (536:536:536))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (154:154:154) (210:210:210))
        (PORT datab (152:152:152) (205:205:205))
        (PORT datad (140:140:140) (182:182:182))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\led\[0\]\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (958:958:958) (1000:1000:1000))
        (PORT d (37:37:37) (50:50:50))
        (PORT ena (506:506:506) (536:536:536))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
      (HOLD ena (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (135:135:135) (185:185:185))
        (IOPATH datab combout (192:192:192) (181:181:181))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (90:90:90) (111:111:111))
        (PORT datad (334:334:334) (384:384:384))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[0\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (136:136:136) (188:188:188))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[1\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (134:134:134) (185:185:185))
        (IOPATH dataa combout (186:186:186) (175:175:175))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[2\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (134:134:134) (184:184:184))
        (IOPATH datab combout (166:166:166) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[3\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (141:141:141) (189:189:189))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[4\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (141:141:141) (189:189:189))
        (IOPATH datab combout (166:166:166) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[5\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (362:362:362) (443:443:443))
        (IOPATH dataa combout (186:186:186) (175:175:175))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (153:153:153) (196:196:196))
        (PORT datad (317:317:317) (369:369:369))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[6\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (751:751:751) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~14\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (142:142:142) (192:192:192))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[7\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~16\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (134:134:134) (183:183:183))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[8\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~18\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (134:134:134) (187:187:187))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[9\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~20\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (132:132:132) (182:182:182))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[10\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~22\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (138:138:138) (188:188:188))
        (IOPATH datab combout (166:166:166) (176:176:176))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (92:92:92) (115:115:115))
        (PORT datad (334:334:334) (384:384:384))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[11\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (753:753:753) (771:771:771))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~24\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (210:210:210) (262:262:262))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (174:174:174) (211:211:211))
        (PORT datad (140:140:140) (173:173:173))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[12\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (751:751:751) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~26\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (209:209:209) (265:265:265))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (154:154:154) (197:197:197))
        (PORT datad (163:163:163) (192:192:192))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[13\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (751:751:751) (769:769:769))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~28\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (210:210:210) (264:264:264))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (152:152:152) (194:194:194))
        (PORT datad (175:175:175) (208:208:208))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[14\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (950:950:950) (997:997:997))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~30\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (142:142:142) (193:193:193))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[15\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~32\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (144:144:144) (196:196:196))
        (IOPATH dataa combout (186:186:186) (175:175:175))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (91:91:91) (113:113:113))
        (PORT datad (197:197:197) (231:231:231))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[16\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (951:951:951) (993:993:993))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~34\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (142:142:142) (193:193:193))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[17\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (752:752:752) (770:770:770))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~36\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (143:143:143) (192:192:192))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (216:216:216) (256:256:256))
        (PORT datad (92:92:92) (110:110:110))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[18\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (951:951:951) (993:993:993))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~38\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (213:213:213) (268:268:268))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~8\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (174:174:174) (210:210:210))
        (PORT datad (138:138:138) (170:170:170))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[19\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (950:950:950) (997:997:997))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~5\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (316:316:316) (379:379:379))
        (PORT datab (142:142:142) (191:191:191))
        (PORT datac (200:200:200) (249:249:249))
        (PORT datad (319:319:319) (378:378:378))
        (IOPATH dataa combout (158:158:158) (163:163:163))
        (IOPATH datab combout (160:160:160) (167:167:167))
        (IOPATH datac combout (119:119:119) (125:125:125))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~40\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (209:209:209) (263:263:263))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~10\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (152:152:152) (192:192:192))
        (PORT datad (161:161:161) (189:189:189))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[20\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (950:950:950) (997:997:997))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~42\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (143:143:143) (195:195:195))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~11\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (216:216:216) (256:256:256))
        (PORT datad (91:91:91) (108:108:108))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[21\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (951:951:951) (993:993:993))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~44\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (223:223:223) (276:276:276))
        (IOPATH datab combout (192:192:192) (177:177:177))
        (IOPATH datab cout (227:227:227) (175:175:175))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~12\\)
    (DELAY
      (ABSOLUTE
        (PORT datac (161:161:161) (193:193:193))
        (PORT datad (137:137:137) (169:169:169))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[22\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (950:950:950) (997:997:997))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~46\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (143:143:143) (193:193:193))
        (IOPATH dataa combout (165:165:165) (173:173:173))
        (IOPATH dataa cout (226:226:226) (171:171:171))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
        (IOPATH cin cout (34:34:34) (34:34:34))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[23\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (951:951:951) (993:993:993))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Add1\~48\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (206:206:206) (252:252:252))
        (IOPATH datad combout (68:68:68) (63:63:63))
        (IOPATH cin combout (187:187:187) (204:204:204))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_counter\~9\\)
    (DELAY
      (ABSOLUTE
        (PORT datab (152:152:152) (193:193:193))
        (PORT datad (171:171:171) (202:202:202))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_counter\[24\]\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (950:950:950) (997:997:997))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~6\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (143:143:143) (193:193:193))
        (PORT datab (141:141:141) (190:190:190))
        (PORT datac (299:299:299) (353:353:353))
        (PORT datad (210:210:210) (259:259:259))
        (IOPATH dataa combout (170:170:170) (163:163:163))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~1\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (370:370:370) (455:455:455))
        (PORT datab (141:141:141) (189:189:189))
        (PORT datac (447:447:447) (518:518:518))
        (PORT datad (459:459:459) (536:536:536))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (120:120:120) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~3\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (142:142:142) (193:193:193))
        (PORT datab (142:142:142) (190:190:190))
        (PORT datac (131:131:131) (173:173:173))
        (PORT datad (305:305:305) (359:359:359))
        (IOPATH dataa combout (170:170:170) (163:163:163))
        (IOPATH datab combout (168:168:168) (167:167:167))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (136:136:136) (189:189:189))
        (PORT datab (134:134:134) (184:184:184))
        (PORT datac (121:121:121) (164:164:164))
        (PORT datad (122:122:122) (161:161:161))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (120:120:120) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~2\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (138:138:138) (192:192:192))
        (PORT datab (136:136:136) (187:187:187))
        (PORT datac (120:120:120) (163:163:163))
        (PORT datad (124:124:124) (164:164:164))
        (IOPATH dataa combout (158:158:158) (157:157:157))
        (IOPATH datab combout (160:160:160) (156:156:156))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~4\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (104:104:104) (136:136:136))
        (PORT datab (105:105:105) (134:134:134))
        (PORT datac (315:315:315) (369:369:369))
        (PORT datad (422:422:422) (483:483:483))
        (IOPATH dataa combout (159:159:159) (163:163:163))
        (IOPATH datab combout (161:161:161) (167:167:167))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\Equal2\~7\\)
    (DELAY
      (ABSOLUTE
        (PORT dataa (105:105:105) (137:137:137))
        (PORT datab (142:142:142) (190:190:190))
        (PORT datac (90:90:90) (111:111:111))
        (PORT datad (90:90:90) (108:108:108))
        (IOPATH dataa combout (159:159:159) (163:163:163))
        (IOPATH datab combout (161:161:161) (167:167:167))
        (IOPATH datac combout (119:119:119) (124:124:124))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "cycloneive_lcell_comb")
    (INSTANCE \\toggle_pin\~0\\)
    (DELAY
      (ABSOLUTE
        (PORT datad (141:141:141) (174:174:174))
        (IOPATH datac combout (190:190:190) (195:195:195))
        (IOPATH datad combout (68:68:68) (63:63:63))
      )
    )
  )
  (CELL
    (CELLTYPE "dffeas")
    (INSTANCE \\toggle_pin\~reg0\\)
    (DELAY
      (ABSOLUTE
        (PORT clk (950:950:950) (997:997:997))
        (PORT d (37:37:37) (50:50:50))
        (IOPATH (posedge clk) q (105:105:105) (105:105:105))
      )
    )
    (TIMINGCHECK
      (HOLD d (posedge clk) (84:84:84))
    )
  )
)
